.loginCard {
  width: 100%;
  height: 192px;
  background-color: var(--bg-light-color, #f5f5f5);
  border-radius: 12px;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  transition: box-shadow 0.2s ease;
}

.cardHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 16px;
  height: 48px;
}

.greetingSection {
  display: flex;
  align-items: center;
  gap: 8px;
}

.greetingIcon {
  width: 32px;
  height: 32px;
}

.greetingText {
  font-size: 16px;
  font-weight: 600;
  color: var(--bg-white-color, rgba(255, 255, 255, 0.96)) !important;
}

.cardContent {
  flex: 1;
  padding: 12px 16px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.contentText {
  text-align: center;
}

.mainText {
  font-size: 16px;
  font-weight: 600;
  color: var(--bg-white-color, rgba(255, 255, 255, 0.96)) !important;
  margin-bottom: 4px;
}

.subText {
  font-size: 12px;
  color: var(--bg-white-color, rgba(255, 255, 255, 0.96)) !important;
  line-height: 16px;
}

.cardFooter {
  padding: 0 16px 16px;
}

.loginButton {
  width: 100%;
  height: 48px;
  background-color: #FF5000;
  color: white;
  font-size: 16px;
  font-weight: 500;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.15s ease;
}

.loginButton:hover {
  background-color: #E6470A;
}

.loginButton:active {
  background-color: #CC3D00;
}

.loginButton:focus {
  outline: none;
}