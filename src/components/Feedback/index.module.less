.container {
  border-radius: 12px;
  background-color: var(--tbpc-feedback-bg-color, #fff);
  border: 1px solid var(--recommend-banner-border, rgba(0, 0, 0, 0.02));
  display: flex;
  flex-direction: column;
  flex-wrap: nowrap;
  box-sizing: border-box;
  padding: 4px;
  align-items: center;
  justify-content: center;
  width: 56px;
  box-shadow: 0 2px 6px 0 var(--tbpc-feedback-shadow-color, rgba(0, 0, 0, 0.08));
  position: fixed;
  bottom: 16px;
  right: 16px;
  z-index: 9;
  cursor: pointer;
}

.item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 48px;
  height: 48px;
  border-radius: 8px;
  margin-bottom: 4px;
  background-color: var(--tbpc-feedback-bg-color, #fff);
  &:hover {
    background-color: var(--tbpc-feedback-bg-color-hover, #f5f5f5);
  }
  &:active {
    background-color: var(--tbpc-feedback-bg-color-active, #ebebeb);
  }
}

.item:last-child {
  margin-bottom: 0;
}

.icon {
  background-size: contain;
  background-position: center;
  background-repeat: no-repeat;
  width: 24px;
  height: 24px;
}

.customServiceIcon {
  background-image: var(--tbpc-custom-icon-image, url(https://img.alicdn.com/imgextra/i2/O1CN01kY7z851Ysip0utQ6y_!!6000000003115-2-tps-96-96.png));
}

.feedbackIcon {
  background-image: var(--tbpc-feedback-icon-image, url(https://img.alicdn.com/imgextra/i1/O1CN01dGQxnE1WutShXPR2J_!!6000000002849-2-tps-96-96.png));
}

.text {
  font-family: PingFang SC;
  font-size: 10px;
  font-weight: normal;
  line-height: 14px;
  height: 14px;
  text-align: center;
  color: var(--tbpc-feedback-text-color, #1f1f1f);
  margin-top: 2px;
}

.backToTopContainer {
  border-radius: 12px;
  background-color: var(--tbpc-feedback-bg-color, #fff);
  border: 1px solid var(--recommend-banner-border, rgba(0, 0, 0, 0.02));
  display: flex;
  align-items: center;
  justify-content: center;
  width: 56px;
  height: 56px;
  box-shadow: 0 2px 6px 0 var(--tbpc-feedback-shadow-color, rgba(0, 0, 0, 0.08));
  position: fixed;
  bottom: 16px;
  right: 88px; /* 56px (feedback width) + 16px (gap) + 16px (right margin) */
  z-index: 9;
  cursor: pointer;
  &:hover {
    background-color: var(--tbpc-feedback-bg-color-hover, #f5f5f5);
  }
  &:active {
    background-color: var(--tbpc-feedback-bg-color-active, #ebebeb);
  }
}

.backToTopIcon {
  background-image: url(https://img.alicdn.com/imgextra/i2/O1CN01a6Vd581jfrTfZlI1f_!!6000000004576-2-tps-96-96.png);
  background-size: contain;
  background-position: center;
  background-repeat: no-repeat;
  width: 24px;
  height: 24px;
}
