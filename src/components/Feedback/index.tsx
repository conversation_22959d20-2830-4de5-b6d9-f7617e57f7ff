import { useState, useEffect } from 'react';
import styles from './index.module.less';

const feedbackUrl = `https://market.m.taobao.com/app/tbhome/feedback/index.html?fromURL=${encodeURIComponent(location?.href || '')}`;
const customServiceUrl = 'https://consumerservice.taobao.com/online-help';

function Feedback() {
    return (
      <div className={styles.container} >
        <a className={styles.item} href={customServiceUrl} target="_blank" data-spm="dcustom">
          <div
            className={`${styles.icon} ${styles.customServiceIcon}`}
          />
          <div
            className={styles.text}
          >
            官方客服
          </div>
        </a>
        <a className={styles.item} href={feedbackUrl} target="_blank" data-spm="dfeedback">
          <div
            className={`${styles.icon} ${styles.feedbackIcon}`}
          />
          <div
            className={styles.text}
          >
            问题反馈
          </div>
        </a>
      </div>
    );
}

function BackToTop() {
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    const checkTabListFixed = () => {
      const smallTabListWrapper = document.querySelector('.smallTabListWrapperFixed');
      setIsVisible(!!smallTabListWrapper);
    };

    // 使用 MutationObserver 监听 DOM 变化
    const observer = new MutationObserver(() => {
      checkTabListFixed();
    });

    // 监听整个 ice-container 的变化
    const iceContainer = document.querySelector('#ice-container');
    if (iceContainer) {
      observer.observe(iceContainer, {
        childList: true,
        subtree: true,
        attributes: true,
        attributeFilter: ['class']
      });
    }

    // 初始检查
    checkTabListFixed();

    return () => {
      observer.disconnect();
    };
  }, []);

  const handleBackToTop = () => {
    window.scrollTo({ top: 0, behavior: 'smooth' });
    setIsVisible(false);
  };

  if (!isVisible) {
    return null;
  }

  return (
    <div className={styles.backToTopContainer} onClick={handleBackToTop}>
      <div className={styles.backToTopIcon} />
    </div>
  );
}

export default Feedback;
export { BackToTop };
