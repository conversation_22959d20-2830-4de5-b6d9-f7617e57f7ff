import styles from './index.module.less';

const feedbackUrl = `https://market.m.taobao.com/app/tbhome/feedback/index.html?fromURL=${encodeURIComponent(location?.href || '')}`;
const customServiceUrl = 'https://consumerservice.taobao.com/online-help';

function Feedback() {
    return (
      <div className={styles.container} >
        <a className={styles.item} href={customServiceUrl} target="_blank" data-spm="dcustom">
          <div
            className={`${styles.icon} ${styles.customServiceIcon}`}
          />
          <div
            className={styles.text}
          >
            官方客服
          </div>
        </a>
        <a className={styles.item} href={feedbackUrl} target="_blank" data-spm="dfeedback">
          <div
            className={`${styles.icon} ${styles.feedbackIcon}`}
          />
          <div
            className={styles.text}
          >
            问题反馈
          </div>
        </a>
      </div>
    );
}

export default Feedback;
