import { useEffect } from 'react';
import { isTrue } from '../../utils';
import { isWapa } from '@/utils/env';
import { $staticConfig } from '@/utils/constant';
import { dispatchTaoCoinTaskEvent } from '@/utils/events';

export interface SearchProps {
  target?: string;
  referer: 'pc_taobao' | 'tmall' | 'enterprise' | 'international';
  fromSource: 'tbClient';
  tabData: Array<{
    value: string;
    label: string;
    action: string;
    spm?: string;
    selected?: boolean;
  }>;
  renderGuessWant: string[];
  tabFix?: string;
  imgSearchButtonShowOutside?: boolean;
  allowPreLoad?: boolean;
  hasHotWordPlugin?: boolean;
  hotDataListFilterHook?: (dataList: any) => any;
}

function Search(props: SearchProps) {
  const {
    referer,
    tabData,
    renderGuessWant,
    tabFix,
    imgSearchButtonShowOutside = true,
    allowPreLoad = true,
    target = '_blank',
    fromSource,
    hasHotWordPlugin = true,
    hotDataListFilterHook,
  } = props;

  useEffect(() => {
    // @ts-ignore
    let forms = document.forms?.search;

    function _initNew(hasRecommendPlugin: boolean) {
      let { SearchSuggest } = window;

      if (!SearchSuggest) return;

      let searchPlugins = [];

      if (hasHotWordPlugin) {
        searchPlugins.push(new SearchSuggest.HotWordPlugin({
          ...tabFix ? { tabFix } : {},
          ...hotDataListFilterHook ? { hotDataListFilterHook } : {},
        }));
      }

      if (hasRecommendPlugin) {
        searchPlugins.push(new SearchSuggest.RecommendPlugin());
      }

      let searchStaticConfig = $staticConfig.search || {};
      let showImageSearch = isTrue(searchStaticConfig.showImageSearch);
      let showImageSearchTip = isTrue(searchStaticConfig.showImageSearchTip);
      let imageSearchTipText = searchStaticConfig.imageSearchTipText || '';

      let searchSuggest = new SearchSuggest('#J_Search', {
        target: target,
        fromSource: fromSource || '',
        allowPreLoad: allowPreLoad,
        isProd: !isWapa,
        imgSearchButtonShowOutside,
        referer: referer || 'pc_taobao',
        imageSearchForbidden: !showImageSearch, // 禁止图片搜索
        stopSearchImageJump: false, // 禁止图片搜索跳转
        disableImgSearchTip: !showImageSearchTip, // 不提示新人提示
        imgSearchTipText: imageSearchTipText || '', // 新人提示文案
        imgSearchJumpDataMode: 'postMessage', // 图搜数据跨域方案: postMessage | cdn
        renderGuessWant: renderGuessWant,
        combobox: {
          popupClassName: 'home-search-popup',
        },
        styleConfig: {
          borderColorFix: true, // 是否固定边框颜色
        },
        tab: {
          data: tabData || [
            {
              value: 'item',
              label: '宝贝',
              selected: true,
              action: '//s.taobao.com/search?tab=all',
            },
            {
              value: 'tmall',
              label: '天猫',
              action: '//s.taobao.com/search?tab=mall',
            },
            {
              value: 'shop',
              label: '店铺',
              action: '//s.taobao.com/search?tab=shop',
            },
          ],
        },
        plugins: searchPlugins,
        sourceUrl: '//suggest.taobao.com/sug?k=1&area=c2c',
      });

      searchSuggest.on('typeChange', (data: any) => {
        let { type } = data;
        forms.search_type.value = type;
      });

      searchSuggest.on('afterSearchSubmit', (data: any) => {
        dispatchTaoCoinTaskEvent('search');
      });
      
      document.addEventListener('click', (event) => {

        try {
          const target = event.target as HTMLElement;
          // 检查点击的是否是热词项
          const hotWordItem = target.closest('[data-sg-type="hotWord"] .item');
          if (hotWordItem) {
            // 触发淘金币任务事件
            dispatchTaoCoinTaskEvent('search');
          }
        } catch (error) {
          console.warn('Search hotword click handler error:', error);
        }

      });
    }
    if (
      window &&
      window.lib &&
      window.lib.privacysdk &&
      window.lib.privacysdk.getSwitchStatus
    ) {
      window.lib.privacysdk
        .getSwitchStatus('TB_PC_Search')
        .then((res: any) => {
          if (!(res && res.switchStatus)) {
            _initNew(false);
          } else {
            _initNew(true);
          }
        })
        .catch((error: any) => {
          _initNew(true);
        });
    } else {
      _initNew(true);
    }
  }, []);

  return (
    <div className="tbh-search J_Module" data-name="search">
      <div className="search-wrap search-suggest-out-wrapper">
        <div className="search-bd search-suggest" id="J_Search">
          <div data-sg-type="tab" />
          <form
            data-sg-type="form"
            target="_top"
            action="//s.taobao.com/search"
            name="search"
            id="J_TSearchForm"
            className="search-panel-focused"
          >
            <i className="search-split" />
            <div className="search-button">
              <button
                className="btn-search tb-bg"
                type="submit"
              >
                搜索
              </button>
            </div>
            <div data-sg-type="placeholder" />
            <div data-sg-type="combobox" className="search-suggest-combobox">
              <input
                id="q"
                name="q"
                aria-label="请输入搜索文字"
                accessKey="s"
                autoFocus
                autoComplete="off"
                aria-haspopup="true"
                role="combobox"
                x-webkit-grammar="builtin:translate"
              />
            </div>
            <input type="hidden" name="commend" value="all" />
            <input type="hidden" name="ssid" value="s5-e" autoComplete="off" />
            <input
              type="hidden"
              name="search_type"
              value="item"
              autoComplete="off"
            />
            <input type="hidden" name="sourceId" value="tb.index" />
            <input type="hidden" name="ie" value="utf8" />
            <input
              type="hidden"
              name="initiative_id"
              value="tbindexz_20170306"
            />
          </form>
        </div>
        {imgSearchButtonShowOutside ? <div className="search-suggest-image-search-out-icon" /> : null}
      </div>
    </div>
  );
}

export default Search;
