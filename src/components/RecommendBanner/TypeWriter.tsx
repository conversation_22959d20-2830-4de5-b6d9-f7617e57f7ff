import React, { useState, useEffect } from 'react';
import styles from './TypeWriter.module.css';

const TypeWriter = ({ text, speed = 100, delay = 0, className = '' }) => {
    const [displayText, setDisplayText] = useState('');
    const [isTyping, setIsTyping] = useState(false);
    const [showCursor, setShowCursor] = useState(false);

    useEffect(() => {
        let delayTimer;

        // Hide cursor during delay
        setShowCursor(false);

        // Delay before starting to type
        delayTimer = setTimeout(() => {
            setIsTyping(true);
            setShowCursor(true);
        }, delay);

        return () => {
            clearTimeout(delayTimer);
        };
    }, [delay]);

    useEffect(() => {
        if (!isTyping) return;

        let i = 0;
        const timer = setInterval(() => {
            if (i < text.length) {
                setDisplayText(text.slice(0, i + 1));
                i++;
            } else {
                clearInterval(timer);
                // Hide cursor after typing is complete
                setTimeout(() => setShowCursor(false), 500); // 500ms delay before hiding cursor
            }
        }, speed);

        return () => clearInterval(timer);
    }, [text, speed, isTyping]);

    return (
      <div className={styles.wrapper}>
        <span className={`${styles.text} ${className}`}>{text}</span>
        <span className={`${styles.typedText} ${className}`}>
          {displayText}
          <span className={`${styles.cursor} ${!showCursor ? styles.cursorHidden : ''}`} />
        </span>
      </div>
    );
};

export default TypeWriter;
