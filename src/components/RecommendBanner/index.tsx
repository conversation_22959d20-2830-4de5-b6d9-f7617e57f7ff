import React, { useCallback, useState, useEffect, useRef, useLayoutEffect } from 'react';
import { PictureWeb } from '@ali/picture';
import { dispatchTaoCoinTaskEvent } from '@/utils/events';
import { throttle } from 'lodash-es';
import styles from './index.module.css';
import { goldlogRecord } from '@/utils/goldlog';
import { getPageSpm } from '@/utils/spm';
import { queryMultiResources } from '@/utils/ald-request';
import Slider from 'react-slick';
import { useUpdateEffectOnce } from '@/hooks/useUpdateEffectOnce';
import TypeWriter from './TypeWriter';

interface RecommendBannerProps {
  id?: string;
}


/**
 * 测量文字宽度
 * @param {string} text 要测量的文字
 * @param {string} font 字体样式,例如 '16px Arial'
 * @return {number} 文字宽度
 */
function measureTextWidth(text, font = '16px PingFang') {
  // 创建canvas元素
  const canvas = document.createElement('canvas');
  const context = canvas.getContext('2d');

  // 设置字体
  context!.font = font;

  // 测量文字宽度
  const metrics = context!.measureText(text);

  return metrics.width;
}


const IFRAME_STYLES = `
  position: fixed;
  top: 0;
  left: 0;
  border: none;
  display: block;
  width: 100vw;
  height: 100vh;
  z-index: 10000;
  background-color: #fff;
  transform: translateY(100%);
  transition: transform 0.3s ease-in-out;
`;

// const CLOSE_BTN_STYLES = `
//   position: fixed;
//   bottom: 20px;
//   flex-direction: row;
//   justify-content: center;
//   left: 50%;
//   display: none;
//   align-items: center;
//   width: 128px;
//   margin-left: -64px;
//   height: 48px;
//   border: 1px solid rgba(0, 0, 0, 0.02);
//   background-color: #F1F3F5;
//   box-shadow: 0px 2px 6px 0px rgba(0, 0, 0, 0.08);
//   border-radius: 12px;
//   font-size: 12px;
//   z-index: 10001;
//   cursor: pointer;
// `;

const defaultItemList: any[] = [
  {
    pictUrl:
      'https://img.alicdn.com/imgextra/i4/O1CN01WG2Q681mzVHpJHS7G_!!6000000005025-2-tps-1080-1440.png_q95.jpg',
  },
  {
    pictUrl:
      'https://img.alicdn.com/imgextra/i2/O1CN01FMI1YF1ROlaqRsNFr_!!6000000002102-2-tps-1080-1440.png_q95.jpg',
  },
  {
    pictUrl:
      'https://img.alicdn.com/imgextra/i2/O1CN01PWq7dv1Bzpn1yBfjv_!!6000000000017-2-tps-1080-1440.png_q95.jpg',
  },
  {
    pictUrl:
      'https://img.alicdn.com/imgextra/i4/O1CN01lStfjd23ZiSbI6W4D_!!6000000007270-2-tps-1080-1440.png_q95.jpg',
  },
  {
    pictUrl:
      'https://img.alicdn.com/imgextra/i2/O1CN011WHLDE1jlMD4XynJG_!!6000000004588-2-tps-1080-1440.png_q95.jpg',
  },
  {
    pictUrl:
      'https://img.alicdn.com/imgextra/i2/O1CN01cimNyL28nWTXp7VgU_!!6000000007977-2-tps-1080-1440.png_q95.jpg',
  },
];

const spmC = 'banner';
const spmD = 'd1';


type HOT_LIST_ALD_RESULT = { pageList: { sourceType: 'UGC' | 'CONFIG'; title: string; themePageId?: number; heatDesc?: string; floorList }[] };
const HOT_LIST_ALD = 38467561;
const DEFAULT_HOT_LIST = [{ title: '发现更多好物' }] as HOT_LIST_ALD_RESULT['pageList'];
const guangUrl =
  location.host.indexOf('pre-') >= 0
    ? 'https://pre-wormhole.wapa.tmall.com/wow/z/tbhome/guang/c6yj86r6f7m4k2dd3jxn?remove_framework=true&show_back_home_btn=false'
    : 'https://guang.taobao.com/?remove_framework=true&show_back_home_btn=false';

const RecommendBanner: React.FC<RecommendBannerProps> = ({ id }) => {
  const reachedBottomRef = useRef(false);
  const [reachedBottom, setReachedBottom] = useState(reachedBottomRef.current);
  const startYRef = useRef(0);
  const isDraggingRef = useRef(false);
  const wheelCountRef = useRef(0);
  const wheelTimerRef = useRef<any>(null);
  const isClosedRef = useRef(false);
  const guangRef = useRef<HTMLIFrameElement | null>(null);

  const currentHotPageNumber = useRef(0);
  const [hotlist, setHotlist] = useState<HOT_LIST_ALD_RESULT['pageList']>([]);
  const [itemList, setItemList] = useState<{ pictUrl: string }[]>(defaultItemList);

  const [containerWidth, setContainerWidth] = useState<number | undefined>();
  const isPageBottom = () => {
    const { scrollHeight } = document.documentElement;
    const scrollTop = window.scrollY || document.documentElement.scrollTop;
    const clientHeight =
      window.innerHeight || document.documentElement.clientHeight;
    return scrollHeight - scrollTop - clientHeight < 10;
  };

  const resetState = useCallback(() => {
    reachedBottomRef.current = false;
    setReachedBottom(false);
  }, []);


  useEffect(() => {
    queryMultiResources<{
      [HOT_LIST_ALD]: { data: HOT_LIST_ALD_RESULT };
    }>({
      resIdList: [HOT_LIST_ALD],
    }).then(ret => {
      const result = ret.data?.[HOT_LIST_ALD].data?.[0] as HOT_LIST_ALD_RESULT | undefined;
      if (result?.pageList?.length) {
        setHotlist(result.pageList);
      } else {
        setHotlist(DEFAULT_HOT_LIST);
      }
    }).catch(err => {
      setHotlist(DEFAULT_HOT_LIST);
    });
  }, []);

  useLayoutEffect(() => {
    onChangeCarousel(0);
  }, [hotlist]);

  useUpdateEffectOnce(() => {
    if (hotlist[0]) {
      // 曝光
      goldlogRecord({
        logKey: '/tbpcclient.newpc.recommend',
        gmKey: 'EXP',
        goKey: {
          spm: getPageSpm(spmC, spmD),
          firstPageId: hotlist[0].themePageId,
          firstSourceType: hotlist[0].sourceType,
        },
      });
    }
  }, [hotlist]);

  useEffect(() => {
    const handleMouseDown = (e: MouseEvent) => {
      isDraggingRef.current = true;
      startYRef.current = e.clientY;
    };

    const handleMouseMoveBase = (e: MouseEvent) => {
      if (!isDraggingRef.current) return;

      const deltaY = e.clientY - startYRef.current;
      // console.log(
      //   'e.clientY, startYRef.current=',
      //   e.clientY,
      //   startYRef.current,
      // );
      // console.log('mouse move deltaY:', deltaY);

      if (deltaY < -120 && isPageBottom() && !reachedBottomRef.current) {
        reachedBottomRef.current = true;
        isDraggingRef.current = false;
        setReachedBottom(true);
      }
    };

    const handleMouseMove = throttle(handleMouseMoveBase, 200);

    const handleMouseUp = () => {
      isDraggingRef.current = false;
    };

    const handleContextMenu = () => {
      isDraggingRef.current = false;
    };

    const handleWheelBase = (e: WheelEvent) => {
      // console.log(
      //   'wheel deltaY:',
      //   e.deltaY,
      //   isPageBottom(),
      //   wheelCountRef.current,
      //   reachedBottomRef.current,
      // );
      if (isPageBottom() && !reachedBottomRef.current) {
        if (wheelTimerRef.current) {
          clearTimeout(wheelTimerRef.current);
        }

        // 单次滚动距离大于120直接触发
        if (e.deltaY > 120) {
          reachedBottomRef.current = true;
          setReachedBottom(true);
          wheelCountRef.current = 0;
          return;
        }

        // 记录滚动次数
        wheelCountRef.current += 1;

        wheelTimerRef.current = setTimeout(() => {
          wheelCountRef.current = 0;
        }, 1000);

        // 滚动次数大于2且滚动距离大于30才触发
        if (wheelCountRef.current >= 2 && e.deltaY > 30) {
          reachedBottomRef.current = true;
          setReachedBottom(true);
          wheelCountRef.current = 0;
        }
      }
    };

    const handleWheel = throttle(handleWheelBase, 200);

    const handleGuangClose = (event) => {
      if (event.data === 'GUANG_PAGE::CLOSE') {
        isClosedRef.current = true;
        if (guangRef.current) {
          guangRef.current.style.transform = 'translateY(100%)';
          guangRef.current.addEventListener(
            'transitionend',
            () => {
              resetState();
              // if (guangRef.current && isClosedRef.current) {
              //   guangRef.current.style.display = 'none';
              // }
            },
            { once: true },
          );
        }
        resetState();
      }
    };

    window.addEventListener('mousedown', handleMouseDown);
    window.addEventListener('mousemove', handleMouseMove);
    window.addEventListener('mouseup', handleMouseUp);
    window.addEventListener('wheel', handleWheel, { passive: false });
    window.addEventListener('message', handleGuangClose);
    window.addEventListener('contextmenu', handleContextMenu);

    return () => {
      window.removeEventListener('mousedown', handleMouseDown);
      window.removeEventListener('mousemove', handleMouseMove);
      window.removeEventListener('mouseup', handleMouseUp);
      window.removeEventListener('wheel', handleWheel);
      window.removeEventListener('message', handleGuangClose);
      window.removeEventListener('contextmenu', handleContextMenu);
      if (wheelTimerRef.current) {
        clearTimeout(wheelTimerRef.current);
      }
    };
  }, [hotlist]);

  const openGuangPageInNewPage = useCallback(() => {
    goldlogRecord({
      logKey: '/tbpcclient.newpc.recommend_new_page',
      gmKey: 'CLK',
      goKey: {
        spm: getPageSpm(spmC, spmD),
        themePageId: hotlist[currentHotPageNumber.current]?.themePageId,
        title: hotlist[currentHotPageNumber.current]?.title,
        sourceType: hotlist[currentHotPageNumber.current]?.sourceType,
      },
    });
    const resUrl = `${guangUrl}&insert_card_sjjsd_themePageId=${hotlist[currentHotPageNumber.current].themePageId}&showNotice=true`;
    window.open(resUrl, '_blank');
  }, [hotlist]);

  const openGuangPage = useCallback(() => {
    goldlogRecord({
      logKey: '/tbpcclient.newpc.recommend',
      gmKey: 'CLK',
      goKey: {
        spm: getPageSpm(spmC, spmD),
        themePageId: hotlist[currentHotPageNumber.current]?.themePageId,
        title: hotlist[currentHotPageNumber.current]?.title,
        sourceType: hotlist[currentHotPageNumber.current]?.sourceType,
      },
    });
    // 发送事件
    dispatchTaoCoinTaskEvent('guang');
    // 重置状态
    isClosedRef.current = false;
    let guang = document.getElementById('guang_frame') as HTMLIFrameElement;
    if (guang) {
      guang.style.display = 'block';
      guang.style.transform = 'translateY(100%)';
    } else {
      guang = document.createElement('iframe');
      guang.id = 'guang_frame';
      guang.src = `${guangUrl}&insert_card_sjjsd_themePageId=${hotlist[currentHotPageNumber.current].themePageId}&showNotice=true`;
      guang.style.cssText = IFRAME_STYLES;
      document.body.appendChild(guang);
    }
    guangRef.current = guang;

    // let closeBtn = document.getElementById('frame_close_btn') as HTMLDivElement;
    // if (!closeBtn) {
    //   closeBtn = document.createElement('div');
    //   closeBtn.id = 'frame_close_btn';
    //   closeBtn.style.cssText = CLOSE_BTN_STYLES;
    //   closeBtn.innerHTML = `
    //     <div style="font-family:PingFang SC; font-size:16px; font-weight:600; letter-spacing:normal; color: rgba(0, 0, 0, 0.92); margin-right:8px;">回到首页</div>
    //     <img style="width: 24px;height: 24px;" src="https://gw.alicdn.com/imgextra/i2/O1CN010RgLC02431nt0jqmC_!!6000000007334-2-tps-96-96.png" />
    //   `;
    //   closeBtn.onclick = () => {
    //     isClosedRef.current = true;
    //     closeBtn.style.display = 'none';
    //     guang.style.transform = 'translateY(100%)';
    //     goldlogRecord({
    //       logKey: '/tbpcclient.newpc.recommend_close',
    //       gmKey: 'CLK',
    //       goKey: {
    //         spm: getPageSpm(spmC, spmD),
    //       },
    //     });
    //     guang.addEventListener(
    //       'transitionend',
    //       () => {
    //         resetState();
    //         guang.style.display = 'none';
    //       },
    //       { once: true },
    //     );
    //   };
    //   document.body.appendChild(closeBtn);
    // }

    requestAnimationFrame(() => {
      guang.style.transform = 'translateY(0)';
      // setTimeout(() => {
      //   if (!isClosedRef.current) {
      //     closeBtn.style.display = 'flex';
      //     goldlogRecord({
      //       logKey: '/tbpcclient.newpc.recommend_close',
      //       gmKey: 'EXP',
      //       goKey: {
      //         spm: getPageSpm(spmC, spmD),
      //       },
      //     });
      //   }
      // }, 1000);
      if (guangRef.current?.contentWindow) {
        guangRef.current.contentWindow.postMessage('GUANG_PAGE::REFRESH', '*');

        if (hotlist[currentHotPageNumber.current]?.themePageId) {
          guangRef.current.contentWindow.postMessage({
            name: 'GUANG_PAGE::INSERT_CARD_SJJSD',
            data: hotlist[currentHotPageNumber.current].themePageId,
          }, '*');
        }
      }
    });
  }, [resetState, hotlist]);

  // useEffect(() => {
  //   if (reachedBottom) {
  //     openGuangPage();

  //     goldlogRecord({
  //       logKey: '/3280.ai_venue.guang_home_has_shown',
  //       gmKey: 'EXP',
  //       goKey: {
  //         spm: getPageSpm(spmC, spmD),
  //       },
  //     });
  //   }
  // }, [reachedBottom, openGuangPage]);

  const clickHandler = useCallback(
    (e) => {
      e.preventDefault();
      e.stopPropagation();
      openGuangPageInNewPage();

      goldlogRecord({
        logKey: '/3280.ai_venue.guang_home_has_mouse_moved',
        gmKey: 'EXP',
        goKey: {
          spm: getPageSpm(spmC, spmD),
        },
      });
    },
    [openGuangPage],
  );

  function onChangeCarousel(number) {
    currentHotPageNumber.current = number;

    if (hotlist.length === 0) return;
    if (hotlist[currentHotPageNumber.current]) {
      const l = hotlist[currentHotPageNumber.current];
      const currentWidth = measureTextWidth(`${l.title} ${l.heatDesc || ''}`);
      setContainerWidth(
        Math.max(
          258,
          148/* 其余元素的长度 */ + (currentWidth),
        ),
      );
      setItemList(
        hotlist[currentHotPageNumber.current]
          .floorList?.map((_f) => ({ pictUrl: _f.floorPicUrl })) || defaultItemList,
      );
    }
  }

  return (
    <div
      className={styles.bannerContainer}
      id={id}
      onClick={clickHandler}
      style={{ width: containerWidth }}
    >
      <div className={styles.bannerContent}>
        {/* <div className={styles.bannerText} /> */}
        <div className={styles.carouselImages}>
          {itemList?.slice(0, 3).map((item, index: number) => (
            <PictureWeb className={styles.img} key={index} style={{ left: index * 16 }} source={item.pictUrl} />
          ))}
        </div>
        <div className={styles.textContainer}>
          <Slider
            className={styles.slider}
            vertical
            verticalSwiping
            slidesToShow={1}
            slidesToScroll={1}
            autoplay
            autoplaySpeed={4000}
            infinite
            dots={false}
            arrows={false}
            beforeChange={(current, next) => { onChangeCarousel(next); }}
          >
            {hotlist.map((page) => (
              <div>
                <div className={styles.bannerTextMore}>
                  {
                    page.sourceType === 'UGC'
                      ? <>
                        <TypeWriter text={page.title} speed={100} />
                        <TypeWriter
                          speed={100}
                          text={page.heatDesc}
                          className={styles.heatDesc}
                          delay={page.title.length * 100 + 500}
                        />
                      </>
                      : <>
                        {page.title}
                        <span className={styles.heatDesc}>{page.heatDesc}</span>
                      </>
                  }
                </div>
              </div>
            ))}
          </Slider>
          <div className={`${styles.arrowIcon}`} />
        </div>
      </div>
    </div >
  );
};

export default RecommendBanner;
