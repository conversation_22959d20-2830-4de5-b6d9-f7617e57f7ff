.wrapper {
  position: relative;
}

.text {
  visibility: hidden;
}

.typedText {
  position: absolute;
  top: 0;
  left: 0;
  display: inline-flex;
  align-items: center;
}

.cursor {
  display: inline-block;
  width: 2px;
  height: 1em;
  background-color: #000;
  animation: blink 0.7s infinite;
  margin-left: 2px;
}

.cursorHidden {
  display: none;
}

@keyframes blink {
  0% {
    opacity: 0;
  }

  50% {
    opacity: 1;
  }

  100% {
    opacity: 0;
  }
}