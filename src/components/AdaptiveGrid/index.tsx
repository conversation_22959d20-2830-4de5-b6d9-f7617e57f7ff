import React from 'react';
import styles from './index.module.css';

interface BlockProps {
  children?: React.ReactNode;
  posId?: number;
  style?: React.CSSProperties;
}

export const SmallBlock: React.FC<BlockProps> = ({ children, style }) => (
  <div className={styles.smallBlock} style={style}>{children}</div>
);

export const MediumBlock: React.FC<BlockProps> = ({ children, style }) => (
  <div className={styles.mediumBlock} style={style}>{children}</div>
);

interface AdaptiveGridProps {
  children: React.ReactNode;
}

const AdaptiveGrid: React.FC<AdaptiveGridProps> = ({ children }) => (
  <div className={styles.gridContainer}>{children}</div>
);

export default AdaptiveGrid;