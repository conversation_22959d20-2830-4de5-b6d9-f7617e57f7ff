.container {
  border-radius: 12px;
  background-color: var(--tbpc-feedback-bg-color, #fff);
  border: 1px solid var(--recommend-banner-border, rgba(0, 0, 0, 0.02));
  display: flex;
  align-items: center;
  justify-content: center;
  width: 56px;
  height: 56px;
  box-shadow: 0 2px 6px 0 var(--tbpc-feedback-shadow-color, rgba(0, 0, 0, 0.08));
  position: fixed;
  bottom: 16px;
  right: 16px; /* 与 Feedback 组件对齐 */
  z-index: 9;
  cursor: pointer;
  &:hover {
    background-color: var(--tbpc-feedback-bg-color-hover, #f5f5f5);
  }
  &:active {
    background-color: var(--tbpc-feedback-bg-color-active, #ebebeb);
  }
}

.icon {
  background-image: var(--tbpc-back-to-top-icon-image, url(https://img.alicdn.com/imgextra/i2/O1CN01a6Vd581jfrTfZlI1f_!!6000000004576-2-tps-96-96.png));
  background-size: contain;
  background-position: center;
  background-repeat: no-repeat;
  width: 24px;
  height: 24px;
}
