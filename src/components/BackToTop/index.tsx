import { useState, useEffect, useCallback, useRef } from 'react';
import { throttle } from 'lodash-es';
import styles from './index.module.less';

function BackToTop() {
  const [isVisible, setIsVisible] = useState(false);
  const observerRef = useRef<MutationObserver | null>(null);
  const isVisibleRef = useRef(false);

  const checkTabListFixed = useCallback(() => {
    const smallTabListWrapper = document.querySelector('.smallTabListWrapperFixed');
    const shouldBeVisible = !!smallTabListWrapper;

    // 只有状态真正改变时才更新
    if (isVisibleRef.current !== shouldBeVisible) {
      isVisibleRef.current = shouldBeVisible;
      setIsVisible(shouldBeVisible);
    }
  }, []);

  // 节流处理，避免频繁检查
  const throttledCheck = useCallback(
    throttle(checkTabListFixed, 100),
    [checkTabListFixed]
  );

  useEffect(() => {
    // 初始检查
    checkTabListFixed();

    // 使用 MutationObserver 监听 DOM 变化
    observerRef.current = new MutationObserver((mutations) => {
      // 只有当变化涉及 class 属性时才检查
      const hasClassChange = mutations.some(mutation =>
        mutation.type === 'attributes' &&
        mutation.attributeName === 'class' &&
        (mutation.target as Element).classList?.contains('smallTabListWrapper')
      );

      if (hasClassChange) {
        throttledCheck();
      }
    });

    // 监听整个 ice-container 的变化，但只关注 class 属性变化
    const iceContainer = document.querySelector('#ice-container');
    if (iceContainer) {
      observerRef.current.observe(iceContainer, {
        childList: false,
        subtree: true,
        attributes: true,
        attributeFilter: ['class']
      });
    }

    return () => {
      if (observerRef.current) {
        observerRef.current.disconnect();
      }
      throttledCheck.cancel();
    };
  }, [checkTabListFixed, throttledCheck]);

  const handleBackToTop = useCallback(() => {
    window.scrollTo({ top: 0, behavior: 'smooth' });
    setIsVisible(false);
    isVisibleRef.current = false;
  }, []);

  if (!isVisible) {
    return null;
  }

  return (
    <div className={styles.container} onClick={handleBackToTop}>
      <div className={styles.item}>
        <div className={styles.icon} />
        <div className={styles.text}>
          回顶部
        </div>
      </div>
    </div>
  );
}

export default BackToTop;
