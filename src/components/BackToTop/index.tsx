import { useState, useEffect } from 'react';
import styles from './index.module.less';

function BackToTop() {
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    const checkTabListFixed = () => {
      const smallTabListWrapper = document.querySelector('.smallTabListWrapperFixed');
      setIsVisible(!!smallTabListWrapper);
    };

    // 使用 MutationObserver 监听 DOM 变化
    const observer = new MutationObserver(() => {
      checkTabListFixed();
    });

    // 监听整个 ice-container 的变化
    const iceContainer = document.querySelector('#ice-container');
    if (iceContainer) {
      observer.observe(iceContainer, {
        childList: true,
        subtree: true,
        attributes: true,
        attributeFilter: ['class']
      });
    }

    // 初始检查
    checkTabListFixed();

    return () => {
      observer.disconnect();
    };
  }, []);

  const handleBackToTop = () => {
    window.scrollTo({ top: 0, behavior: 'smooth' });
    setIsVisible(false);
  };

  if (!isVisible) {
    return null;
  }

  return (
    <div className={styles.container} onClick={handleBackToTop}>
      <div className={styles.item}>
        <div className={styles.icon} />
        <div className={styles.text}>
          回顶部
        </div>
      </div>
    </div>
  );
}

export default BackToTop;
