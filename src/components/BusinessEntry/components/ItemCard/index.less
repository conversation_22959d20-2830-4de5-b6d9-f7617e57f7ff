.business-entry-item-card {
  position: relative;

  overflow: hidden;

  width: 100%;
  height: 192px;

  border-radius: 12px;
  background-repeat: no-repeat;
  background-position: center;
  background-size: cover;

  font-size: 0;
}

.business-entry-item-card-content-container {
  display: flex;
  overflow: hidden;
  align-items: center;
  flex-direction: row;
  flex-wrap: wrap;

  box-sizing: border-box;
  width: 100%;
  height: 128px;
  padding-right: 16px;
  padding-left: 16px;

  text-decoration: none;
}

.business-entry-item-card-content {
  position: relative;
  display: flex;
  overflow: hidden;
  align-items: center;
  flex: 1;
  flex-direction: column;
  box-sizing: border-box;
  min-width: 84px;
  // max-width: 180px;
  height: 128px;
  cursor: pointer;
  text-decoration: none;
  border-radius: 8px;
  border-radius: 8px;
}

.business-entry-item-card-content-container-image {
  overflow: hidden;
  flex-shrink: 0;
  position: relative;

  width: 84px;
  height: 84px;
  background-color: var(--bg-color, #fff);

  border-radius: 4px;

  img {
    overflow: hidden;

    width: 84px;
    height: 84px;

    border-radius: 4px;
    background-color: var(--bg-color, #fff);
  }
}

.business-entry-item-card-content-container-image-mask {
  width: 84px;
  height: 84px;
  background-color: rgba(0,0,0,0.03);

  position: absolute;
  top: 0;
  left: 0;
}

.business-entry-item-card-content-title-writing {
  width: 100%;
  margin-top: 2px;
  margin-left: 12px;
}

.business-entry-item-card-content-title {
  display: -webkit-box;
  overflow: hidden;
  -webkit-box-orient: vertical;

  width: 100px;
  height: 40px;

  text-overflow: ellipsis;
  word-break: break-all;

  color: var(--primary-color, #1f1f1f);

  font-size: 14px;
  line-height: 22px;

  -webkit-line-clamp: 2;
}

.business-entry-large-card-top-title-tag {
  float: left;

  box-sizing: border-box;
  margin-left: 8px;

  color: var(--bg-color, #fff);
  border-radius: 4px;
  // background-color: var(--tb-brand-light-color, #ff5000);

  font-size: 12px;
  font-weight: 500;
  line-height: 0;

  img {
    object-fit: contain;
  }
}

.business-entry-item-card-content-reduction-subImg {
  float: left;

  width: 8px;
  height: 16px;
  margin-right: 4px;

  line-height: 16px;
}


.business-entry-item-card-content-coin {
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: baseline;

  width: 100%;
  margin-top: 4px;

  white-space: nowrap;

  color: var(--tb-brand-light-color, #ff5000);

  font-family: 'Inter V', sans-serif;
  font-size: 16px;
  font-weight: 600;
  line-height: 20px;
  letter-spacing: -0.4px;
}

.business-entry-item-card-content-coin-title {
  margin-right: 2px;

  color: var(--tb-brand-light-color, #ff5000);

  font-size: 14px;
  line-height: 18px;
}

.business-entry-item-card-content-coin-suffix {
  position: relative;

  white-space: nowrap;

  color: var(--tb-brand-light-color, #ff5000);

  font-size: 14px;
  font-weight: bold;
  line-height: 18px;
}

.business-entry-item-card-content-reduction {
  display: flex;
  overflow: hidden;
  flex-direction: row;
  flex-grow: 0;
  flex-shrink: 0;
  flex-wrap: wrap;
  justify-content: center;

  width: 100%;
  height: 16px;

  text-align: center;
  vertical-align: middle;
  letter-spacing: 0;
  word-break: break-all;

  color: var(--tb-brand-light-color, #ff5000);
  border-radius: 4px;

  font-size: 12px;
  font-weight: 400;
  line-height: 16px;
}

.business-entry-item-card-item-bg {
  background-color: var(--bg-color, #fff);
}