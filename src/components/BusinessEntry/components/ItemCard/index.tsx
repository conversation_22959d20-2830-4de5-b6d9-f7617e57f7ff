import { useMemo } from 'react';
import './index.less';

import { AppearWeb } from '@ali/appear';
import { getValidString } from '../../../../utils/format';
import { optimizeImage } from '../../../../utils/image';
import { reportCustom } from '../../../../utils/jstracker';
import type { IBaseCardProps } from '../../index';
import { getValidLengthCardData } from '../../lib/card';
import { mergeSjjsdParams } from '../../lib/sjjsd';
import { goldLogBusinessEntryClk, goldLogBusinessEntryExp } from '../../lib/tracker';
import Loading from '../../Loading';
import type { CommonCardDetail } from '../../typings';
import CardTitle from '../CardTitle';

const DefaultSpmC = 'yingxiao';
const DefaultScm = '1007.home_pc.yingxiao.item';

const ItemCard = (props: IBaseCardProps) => {
  const { style, posId, aldData, cachedData, zebraData, backupData, cardType } = props;
  const sliceCount = cardType === 'item_large' ? 4 : 2;

  const validCard = getValidLengthCardData({
    card: aldData,
    cachedCard: cachedData,
    zebraCard: zebraData,
    backupCard: backupData,
    length: sliceCount,
  });
  const validItems = (validCard?.cardData || []) as CommonCardDetail[];

  if (!aldData?.cardData?.length) {
    reportCustom({
      code: 'module-data',
      message: `[yingxiao][${cardType}][alddata-not-enough]`,
      sampling: 1,
      c1: JSON.stringify(props),
    });
  }

  const slicedData = useMemo(() => {
    return validItems.slice(0, sliceCount);
  }, [validItems]);

  if (!slicedData?.length) {
    reportCustom({
      code: 'empty-transformer-area',
      message: `[yingxiao][${cardType}][sliceddata-empty]`,
      sampling: 1,
      c1: JSON.stringify(props),
    });
    return <Loading key={props.posId} style={props.style} />;
  }

  return (
    <AppearWeb
      onFirstAppear={() => {
        goldLogBusinessEntryExp(posId, cardType || '', {
          scm: DefaultScm,
          custom_content_source: validCard?.customContentSource || '',
        });
      }}
    >
      <div
        className="business-entry-item-card"
        data-spm={DefaultSpmC}
        style={{
          ...(style || {}),
          backgroundColor: validCard?.cardColor || style?.backgroundColor,
          backgroundImage: validCard?.cardBackground ? `url(${validCard?.cardBackground})` : '',
        }}
      >
        <CardTitle
          cardPrefixIcon={validCard?.cardPrefixIcon || ''}
          cardTitleColor={validCard?.cardTitleColor || ''}
          cardTitle={validCard?.cardTitle || ''}
          cardJumpUrl={
            mergeSjjsdParams(
              validCard?.cardJumpUrl || '',
              slicedData.map((_d) => _d.itemId),
              validCard?.customContentSource ? { custom_content_source: validCard?.customContentSource || '' } : {},
            ) || ''
          }
          posId={posId}
          contentType={cardType || ''}
          scm={DefaultScm}
          customContentSource={validCard?.customContentSource}
          cardIcon={validCard?.cardIcon}
        />
        <div className="business-entry-item-card-content-container">
          {slicedData?.length
            ? slicedData.map((item, index: number) => (
              <AppearWeb
                key={index}
                onFirstAppear={() => {
                    goldLogBusinessEntryExp(`${posId}_${index + 1}`, cardType || '', {
                      scm: DefaultScm,
                      custom_content_source: validCard?.customContentSource || '',
                    });
                  }}
              >
                <a
                  key={item.itemId}
                  className={`business-entry-item-card-content ${validCard?.cardBackground ? 'business-entry-item-card-item-bg' : ''}`}
                  style={{
                      marginLeft: index === 0 ? 0 : '8px',
                    }}
                  href={
                      validCard?.jumpMode === 'itemMode' && item.clickUrl
                        ? mergeSjjsdParams(
                            item.clickUrl || '',
                            [item.itemId],
                            validCard?.customContentSource
                              ? { custom_content_source: validCard?.customContentSource || '' }
                              : {},
                          ) || ''
                        : mergeSjjsdParams(
                            validCard?.cardJumpUrl || '',
                            slicedData.map((_d) => _d.itemId),
                            validCard?.customContentSource
                              ? { custom_content_source: validCard?.customContentSource || '' }
                              : {},
                          ) || ''
                    }
                  target="_blank"
                  data-spm={`d${posId}_${index + 1}`}
                  onClick={(e) => {
                      e.stopPropagation();
                      goldLogBusinessEntryClk(`${posId}_${index + 1}`, cardType || '', {
                        scm: DefaultScm,
                        custom_content_source: validCard?.customContentSource || '',
                      });
                    }}
                >
                  <div className="business-entry-item-card-content-container-image">
                    {getValidString(item.itemWhiteImg) ? (
                      <img
                        style={
                            validCard?.cardBackground
                              ? {
                                  width: '80px',
                                  height: '80px',
                                  marginTop: '4px',
                                  marginLeft: '2px',
                                }
                              : {}
                          }
                        src={optimizeImage(item.itemWhiteImg)}
                      />
                      ) : null}
                    {validCard?.cardBackground ? null : (
                      <div className="business-entry-item-card-content-container-image-mask" />
                      )}
                  </div>
                  <div
                    className="business-entry-item-card-content-coin"
                    style={{ marginTop: validCard?.cardBackground ? '4px' : '8px' }}
                  >
                    {getValidString(item.price) ? (
                      <>
                        <span className="business-entry-item-card-content-coin-title">¥</span>
                        <span>{item.price || ''}</span>
                      </>
                      ) : null}
                    {getValidString(item.priceSuffix) ? (
                      <span className="business-entry-item-card-content-coin-suffix">{item.priceSuffix}</span>
                      ) : null}
                  </div>
                  <div className="business-entry-item-card-content-reduction">
                    {typeof item.benefit === 'object' &&
                      item.benefit?.length &&
                      getValidString(item.benefit[0]?.benefit) &&
                      item.benefit[0]?.type === 'text' ? (
                        <span
                          style={
                            item.benefit[0].subToolType === 'zflj' || item.benefit[0].subToolType === 'zfbtAddr'
                              ? { color: '#0EBE57' }
                              : {}
                          }
                        >
                          {item.benefit[0].benefit}
                        </span>
                      ) : (
                        getValidString(item.benefit as string) || '火爆热卖中'
                      )}
                  </div>
                </a>
              </AppearWeb>
              ))
            : null}
        </div>
      </div>
    </AppearWeb>
  );
};

export default ItemCard;
