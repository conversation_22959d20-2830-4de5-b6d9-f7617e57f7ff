import { useMemo } from 'react';
import './index.less';

import { AppearWeb } from '@ali/appear';
import BbsEnter from '@ali/pc-bbs-enter';
import { getValidString } from '../../../../utils/format';
import { reportCustom } from '../../../../utils/jstracker';
import { dispatchTaoCoinTaskEvent } from '../../../../utils/events';
import type { IBaseCardProps } from '../../index';
import { getValidLengthCardData } from '../../lib/card';
import { goldLogBusinessEntryClk, goldLogBusinessEntryExp } from '../../lib/tracker';
import Loading from '../../Loading';
import type { BBSCardDetail } from '../../typings';
import CardTitle from '../CardTitle';

const DefaultSpmC = 'yingxiao';
const DefaultScm = '1007.home_pc.taojianghu.item';

const BBS_HOME_ADDRESS = 'https://bbs.taobao.com/home.html';
const sliceCount = 5;

const BbsCard = (props: IBaseCardProps) => {
  const { style, posId, aldData, cachedData, zebraData, backupData } = props;

  const validCard = getValidLengthCardData({
    card: aldData,
    cachedCard: cachedData,
    zebraCard: zebraData,
    backupCard: backupData,
    length: sliceCount,
  });
  const validItems = (validCard?.cardData || []) as BBSCardDetail[];

  if (!aldData?.cardData?.length) {
    reportCustom({
      code: 'module-data',
      message: '[bbs][ald-data][empty]',
      sampling: 1,
      c1: JSON.stringify(props),
    });
  }

  const slicedData = useMemo(() => {
    return validItems.slice(0, sliceCount);
  }, [validItems]);

  if (!slicedData?.length) {
    reportCustom({
      code: 'empty-transformer-area',
      message: '[bbs][data][sliceddata-empty]',
      sampling: 1,
      c1: JSON.stringify(props),
    });
    return <Loading key={props.posId} style={props.style} />;
  }

  return (
    <AppearWeb
      onFirstAppear={() => {
        goldLogBusinessEntryExp(posId, 'bbs', { scm: DefaultScm });
      }}
    >
      <div
        className="business-entry-bbs-card"
        data-spm={DefaultSpmC}
        style={{
          ...(style || {}),
          backgroundColor: validCard?.cardColor || style?.backgroundColor,
        }}
        onClick={() => {
          // 触发淘金币任务事件
          dispatchTaoCoinTaskEvent('bbs');
        }}
      >
        <CardTitle
          cardPrefixIcon={validCard?.cardPrefixIcon || ''}
          cardTitleColor={validCard?.cardTitleColor || ''}
          cardTitle={validCard?.cardTitle || '淘江湖'}
          cardJumpUrl={validCard?.cardJumpUrl || BBS_HOME_ADDRESS}
          cardIcon={validCard?.cardIcon}
          posId={posId}
          contentType={'bbs'}
          scm={DefaultScm}
        />
        <BbsEnter
          isClient
          posId={posId}
          slicedData={slicedData}
          handleClick={(index) => {
            // 触发淘金币任务事件
            dispatchTaoCoinTaskEvent('bbs');
            goldLogBusinessEntryClk(`${posId}_${index + 1}`, 'bbs', {
              scm: DefaultScm,
            });
          }}
          handleAppear={(index) => {
            goldLogBusinessEntryExp(`${posId}_${index + 1}`, 'bbs', {
              scm: DefaultScm,
            });
          }}
        />
      </div>
    </AppearWeb>
  );
};

export default BbsCard;
