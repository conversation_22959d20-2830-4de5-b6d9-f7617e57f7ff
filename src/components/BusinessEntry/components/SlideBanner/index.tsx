import { useRef } from 'react';
import './index.less';

import type { Settings as SliderSettings } from 'react-slick';
import Slider from '../../../Slick/slider.js';
import 'slick-carousel/slick/slick.css';

import { goldlogRecord } from '../../../../utils/goldlog';
import { getPageSpm } from '../../../../utils/spm';
import type { IBaseProps } from '../../LayoutMap';
import { useBusinessEntryData } from '../../hooks/useBusinessEntryData';

const DefaultSpmC = 'banner';

const DefaultSliderSettings: SliderSettings = {
  dots: true,
  infinite: true,
  autoplay: true,
  autoplaySpeed: 5000,
};

const SlideBanner = (props: IBaseProps) => {
  const { style } = props;

  const hasReported = useRef<Record<number, boolean>>({});

  const { advertisements } = useBusinessEntryData();
  function recordExp(targetIndex: number) {
    if (!hasReported.current[targetIndex]) {
      goldlogRecord({
        logKey: '/tbindex.newpc.headfocus',
        gmKey: 'EXP',
        goKey: {
          spm: getPageSpm(DefaultSpmC, `d${targetIndex + 1}`),
          scm: advertisements[targetIndex]?.scm || '',
          custom_content_source: advertisements[targetIndex]?.custom_content_source || '',
        },
      });
      hasReported.current[targetIndex] = true;
    }
  }

  return (
    <div className="business-entry-slide-banner" style={style} data-spm="banner">
      {advertisements.length > 0 ? (
        <Slider
          className={`home-ad-banner ${advertisements.length === 1 ? 'home-ad-banner-single' : ''}`}
          {...DefaultSliderSettings}
          onInit={() => {
            recordExp(0);
          }}
          afterChange={(current: number) => {
            recordExp(current);
          }}
        >
          {advertisements.map((dataItem, dataIndex) => {
            return (
              <div key={dataIndex}>
                <a
                  className="home-ad-banner-item"
                  style={{
                    backgroundColor: dataItem.backgroundColor,
                    backgroundImage: `url(${dataItem.backgroundImage})`,
                    height: '192px',
                  }}
                  href={dataItem.jumpUrl}
                  data-spm={`d${dataIndex + 1}`}
                  onClick={() => {
                    goldlogRecord({
                      logKey: '/tbindex.newpc.headfocus',
                      gmKey: 'CLK',
                      goKey: {
                        spm: getPageSpm(DefaultSpmC, `d${dataIndex + 1}`),
                        scm: dataItem?.scm || '',
                        custom_content_source: dataItem?.custom_content_source || '',
                      },
                    });
                  }}
                >
                  <div
                    className="home-ad-banner-item-content-container"
                    style={{ color: `${dataItem?.titleColor || `rgba(0,0,0,0.88)`}` }}
                  >
                    {dataItem?.topLogo && <img src={dataItem?.topLogo} className="home-ad-banner-item-logo" height={16} />}
                    {dataItem?.title1 && <div className="home-ad-banner-item-title-top">{dataItem?.title1}</div>}
                    {dataItem?.title2 && <div className="home-ad-banner-item-title-middle">{dataItem?.title2}</div>}
                    {dataItem?.subtitle1 && <div className="home-ad-banner-item-subtitle">{dataItem?.subtitle1 || ''}</div>}
                  </div>
                  {!dataItem?.backgroundImage && dataItem.itemImage ? (
                    <div
                      className="home-ad-banner-item-pic"
                      style={{ backgroundImage: `url(${dataItem.itemImage})` }}
                    />
                  ) : null}
                </a>
              </div>
            );
          })}
        </Slider>
      ) : null}
    </div>
  );
};

export default SlideBanner;
