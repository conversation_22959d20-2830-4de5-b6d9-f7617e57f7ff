.business-entry-slide-banner {
  position: relative;

  overflow: hidden;

  width: 100%;
  height: 192px;

  border-radius: 12px;
}

.home-ad-banner {
  z-index: 1;

  width: 100%;
  height: 100%;
}

.home-ad-banner-item {
  position: relative;

  display: block;
  overflow: hidden;

  width: 100%;
  height: 100%;

  border-radius: 12px;
  background-repeat: no-repeat;
  background-position: center;
  background-size: cover;
}

.home-ad-banner-item-content-container {
  position: relative;
  z-index: 1;

  width: calc(100% - 16px);
  margin-top: 16px;
  margin-left: 16px;
}

.home-ad-banner-item-logo {
  margin-bottom: 8px;
}

.home-ad-banner-item-title-top {
  overflow: hidden;

  height: 32px;

  font-size: 24px;
  font-weight: bold;
  line-height: 32px;
}

.home-ad-banner-item-title-middle {
  overflow: hidden;

  height: 32px;
  margin-top: -2px;

  font-size: 24px;
  font-weight: bold;
  line-height: 32px;
}

.home-ad-banner-item-subtitle {
  overflow: hidden;

  height: 22px;
  margin-top: 4px;

  font-size: 14px;
  line-height: 22px;
}

.home-ad-banner-item-pic {
  position: absolute;
  top: 0;
  left: 0;

  width: 240px;
  height: 192px;
  margin-left: 40%;

  background-repeat: no-repeat;
  background-position: center;
  background-size: cover;
}

.slick-slider {
  button {
    display: block;

    margin: 0;
    padding: 0;

    cursor: pointer;

    color: transparent;
    border: none;
    outline: none;
    background-color: transparent;

    font-size: 0;
  }

  .slick-dots {
    position: absolute;
    z-index: 9;
    bottom: 20px;
    left: 16px;

    display: block;

    width: 100%;
    margin: 0;
    padding: 0;

    list-style: none;

    text-align: left;

    font-size: 0;

    li {
      position: relative;

      display: inline-block;
      overflow: hidden;

      width: 6px;
      height: 6px;
      margin: 0 4px;
      padding: 0;

      transition: width 0.2s linear;

      opacity: 0.6;
      border: 1px solid var(--tbpc-bg1-alpha-color, rgba(0, 0, 0, 0.04));
      border-radius: 6px;
      background-color: var(--tbpc-bg1-color, #f5f5f5);

      &.slick-active {
        width: 12px;

        opacity: 1;
        background-color: var(--bg-color, #fff);
      }
    }

    button {
      width: 100%;
      height: 100%;
    }
  }

  &:hover {
    .slick-prev,
    .slick-next {
      display: block !important;
    }
  }

  .slick-prev,
  .slick-next {
    position: absolute;
    z-index: 9;
    top: 50%;

    display: none !important;

    width: 32px;
    height: 32px;

    -webkit-transform: translate(0, -50%);
    -ms-transform: translate(0, -50%);
    transform: translate(0, -50%);

    background-repeat: no-repeat;
    background-position: center;
    background-size: contain;
  }

  .slick-prev {
    left: 0;

    background-image: url(https://gw.alicdn.com/imgextra/i1/O1CN01WnpSnB1DqKgkqjvAy_!!6000000000267-2-tps-64-64.png);
  }

  .slick-next {
    right: 0;

    background-image: url(https://gw.alicdn.com/imgextra/i3/O1CN01LYDfty1FQLRndh0vh_!!6000000000481-2-tps-64-64.png);
  }

  &.home-ad-banner-single {
    .slick-dots,
    .slick-prev,
    .slick-next {
      display: none !important;
    }
  }
}