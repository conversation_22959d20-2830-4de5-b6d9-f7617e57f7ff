import React from 'react';
import './index.less';
import { goldLogBusinessEntryClk } from '../../lib/tracker';

export interface IBusinessEntryTitleProps {
  // 卡片前面的icon
  cardPrefixIcon?: string;
  // 标题颜色
  cardTitleColor?: string;
  // 卡片标题
  cardTitle: string;
  // 标题后的icon
  cardIcon?: string;
  // 跳转链接
  cardJumpUrl: string;
  // 上报相关参数
  posId: number;
  contentType: string;
  scm?: string;
  customContentSource?: string;
}

function CardTitle(props: IBusinessEntryTitleProps) {
  const {
    posId,
    cardJumpUrl,
    cardTitle,
    cardIcon,
    scm,
    customContentSource,
    contentType,
    cardPrefixIcon,
    cardTitleColor,
  } = props;
  return (
    <a
      className="business-entry-title-card-top"
      href={cardJumpUrl}
      target="_blank"
      data-spm={`d${posId}`}
      onClick={() => {
        goldLogBusinessEntryClk(posId, contentType, {
          scm: scm || '',
          custom_content_source: customContentSource || '',
        });
      }}
    >
      <div className="business-entry-title-card-top-title-container">
        <div className="business-entry-title-card-top-title-rtl">
          <div
            className="business-entry-title-card-top-title"
            style={{ color: cardTitleColor || 'var(--primary-color, #1f1f1f)' }}
          >
            {cardTitle}
          </div>
          {cardPrefixIcon ? (
            <div className="business-entry-title-card-top-title-tag-left">
              <img src={cardPrefixIcon} alt="" height={16} />
            </div>
          ) : null}
        </div>
        {cardIcon ? (
          <div className="business-entry-title-card-top-title-tag">
            <img src={cardIcon} alt="" height={16} />
          </div>
        ) : null}
      </div>

      <div className="business-entry-title-card-top-image">
        {/* <span className="tb-ifont mytao-icon" style={{ color: cardTitleColor || '#000' }}>
          &#xe642;
        </span> */}
      </div>
    </a>
  );
}

export default CardTitle;
