.business-entry-live-card {
  position: relative;

  overflow: hidden;

  width: 100%;
  height: 192px;

  border-radius: 12px;

  font-size: 0;
}

.business-entry-live-card-top {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;

  width: 100%;
  height: 16px;
  margin-bottom: 12px;
}

.business-entry-live-card-top-title-tag-container {
  display: flex;
  flex-direction: row;
  align-items: center;
  flex-wrap: wrap;
  height: 16px;
  overflow: hidden;
}

.business-entry-live-card-top-title-tag {
  box-sizing: border-box;
  margin-left: 8px;

  color: var(--bg-color, #fff);
  border-radius: 4px;

  font-size: 12px;
  font-weight: 500;
  line-height: 0;

  img {
    object-fit: contain;
  }
}

.business-entry-live-card-top-title {
  flex-shrink: 0;
  color: var(--primary-color, #1f1f1f);

  font-size: 16px;
  font-weight: bold;
  line-height: 16px;
}

.business-entry-live-card-top-image {
  width: 8px;
  height: 8px;

  line-height: 8px;

  img {
    width: 8px;
    height: 8px;
    margin-top: 4px;
  }

  span {
    width: 8px;
    height: 8px;
    margin-top: 4px;

    transform: rotateZ(-90deg);

    opacity: 0.5;

    font-size: 8px;
  }
}

.business-entry-live-card-content-container {
  box-sizing: border-box;
  padding-left: 16px;
  padding-right: 16px;
  display: flex;
  overflow: hidden;
  flex-direction: row;
  flex-wrap: wrap;

  width: 100%;
  height: 128px;
}

.business-entry-live-card-content {
  position: relative;
  display: flex;
  overflow: hidden;
  flex: 1;
  box-sizing: border-box;
  min-width: 84px;
  // max-width: 180px;
  height: 128px;
  cursor: pointer;
  text-decoration: none;
  border-radius: 8px;
}

.business-entry-live-card-inner {
  position: relative;

  overflow: hidden;

  width: 100%;
  height: 100%;

  border-radius: 8px;
  // background-color: var(--tbpc-divider-alpha-color, rgba(0, 0, 0, 0.08));
  background-repeat: no-repeat;
  background-position: center;
  background-size: cover;
}

.business-entry-live-card-inner-mask {
  position: absolute;
  right: 0;
  bottom: 0;
  left: 0;

  height: 30px;

  background-image: linear-gradient(180deg, rgba(0, 0, 0, 0) 0%, rgba(0, 0, 0, 0.4) 100%);
  background-repeat: no-repeat;
  background-size: cover;
}

.business-entry-live-card-inner-title {
  position: absolute;
  bottom: 8px;
  left: 8px;

  overflow: hidden;

  width: 80px;
  height: 14px;

  text-overflow: clip;
  word-break: break-all;

  color: #fff;

  font-size: 12px;
  font-weight: 500;
  line-height: 14px;
}

.business-entry-live-card-inner-atmosphere {
  position: absolute;
  top: 4px;
  left: 4px;

  width: 16px;
  height: 16px;

  border-radius: 4px;
}
