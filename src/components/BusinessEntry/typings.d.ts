/// <reference types="@ice/pkg/types" />

interface AdDetailNew {
  itemImage?: string;
  backgroundColor?: string;
  backgroundImage?: string;
  jumpUrl: string;
  subtitle1?: string;
  title1: string;
  title2?: string;
  titleColor?: string;
  topLogo: string;
  source?: string;
  scm?: string;
  custom_content_source?: string;
}

export interface AldAdvertisement {
  backgroundColor?: string;
  subtitle1?: string;
  backgroundImage?: string;
  currentAldResId: string;
  title1: string;
  title2?: string;
  topLogo: string;
  jumpUrl: string;
  itemImage?: string;
  distinctId: string;
  titleColor?: string;
  dataSetId: number;
  custom_content_source?: string;
  id: string;
  __pos__: number;
  __track__: string;
}

interface AdDetail {
  imagesUrl: {
    aspectRatioGT1: string;
    aspectRatioLT1: string;
  };
  targetUrl: string;
  scm?: string;
  source: string;
  ext?: Record<string, any>;
}

interface LiveCardDetail {
  imgUrl: string;
  liveExtUrl: string;
  liveHallUrl: string;
  liveId: string;
  title: string;
}

interface BBSCardDetail {
  title: string;
  jumpUrl: string;
  icon: string;
}

interface CommonCardDetail {
  benefit: string | { benefit: string; subToolType: string; type: string }[];
  clickUrl: string;
  currentAldResId: string;
  dataSetId: string;
  dataSetType: string;
  distinctId: string;
  itemId: string;
  itemWhiteImg: string;
  moduleColor: string;
  moduleJumpUrl: string;
  moduleName: string;
  price: string;
  priceSuffix: string;
  shortTitle: string;
  title: string;
  type: string;
  icon: string;
  __pos__: string;
}

export interface CardData<
  T = CommonCardDetail[] | BBSCardDetail[] | LiveCardDetail[],
> {
  cardColor: string;
  cardIcon?: string;
  cardData: T;
  cardJumpUrl: string;
  jumpMode: 'cardMode' | 'itemMode';
  cardTitle: string;
  // 卡片标题颜色
  cardTitleColor?: string;
  // 卡片背景图
  cardBackground?: string;
  // 卡片前面的icon
  cardPrefixIcon?: string;
  cardType: 'item_large' | 'item_small' | 'bbs' | 'live';
  customContentSource?: string;
  __pos__: 1 | 2 | 3 | 4 | 5;
}
export interface CommonCard extends CardData {
  cardData: CommonCardDetail[];
  cardType: 'item_large' | 'item_small';
}

export interface BBSCard extends CardData {
  cardData: BBSCardDetail[];
  cardIcon: string;
  cardType: 'bbs';
}

export interface LiveCard extends CardData {
  cardData: LiveCardDetail[];
  cardType: 'live';
}