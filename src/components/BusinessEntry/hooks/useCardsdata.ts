// import { size } from 'lodash-es';
// import { $staticConfig } from '../../../utils/constant';
import type { CardData } from '../typings';
import { requestToLocalCache } from '@/utils/backup';
import { useMemo, useState } from 'react';

// const transfromBackupLiveCard = (card: CardData<CommonCardDetail[]>): CardData<LiveCardDetail[]> => {
//   const newCardData =
//     card.cardData?.map((_item) => {
//       return {
//         liveHallUrl: _item.clickUrl || _item.moduleJumpUrl,
//         imgUrl: _item.itemWhiteImg,
//         liveId: '',
//         liveExtUrl: '',
//         title: '',
//       };
//     }) || [];

//   return {
//     ...card,
//     cardData: newCardData,
//   };
// };

// const transfromBackupBbsCard = (card: CardData<CommonCardDetail[]>): CardData<BBSCardDetail[]> => {
//   const newCardData: BBSCardDetail[] =
//     card.cardData?.map((_item) => {
//       return {
//         title: _item.shortTitle || _item.title,
//         jumpUrl: _item.clickUrl || _item.moduleJumpUrl,
//         icon: _item.itemWhiteImg || _item.icon,
//       };
//     }) || [];

//   return {
//     ...card,
//     cardData: newCardData,
//   };
// };

// const backupdataFromZebra = (
//   size($staticConfig.transformerareaBackup)
//     ? $staticConfig.transformerareaBackup.map((_card: CardData<CommonCardDetail[]>) => {
//         if (_card.cardType === 'live') {
//           return transfromBackupLiveCard(_card);
//         } else if (_card.cardType === 'bbs') {
//           return transfromBackupBbsCard(_card);
//         }
//         return _card;
//       })
//     : []
// ) as CardData[];

const backupdata = [
  {
    cardData: [
      {
        clickUrl:
          '//item.taobao.com/item.htm?id=************&fromChannel=bybtChannel&skuId=5256945166181&fpChannel=101&fpChannelSig=acafe065c49a424242be46fa65758ef80bd48ff1&from=bybt&utparam=%7B%22x_object_type%22%3A%22item%22%2C%22ald_res%22%3A%2233498784%22%2C%22tpp_buckets%22%3A%22302%230%23273555%230_30636%230%23273555%230%22%2C%22floorId%22%3A38471623%2C%22ald_solution%22%3A%22commonPersonalizedSolution%22%2C%22pvid%22%3A%225019bd4c-1418-45b2-a63e-ff3d7a4331da%22%2C%22ald_biz%22%3A443%2C%22scm%22%3A%221007.10302.273555.38471623_0_0%22%2C%22traceId%22%3A%222150408e17217006657124340e3728%22%2C%22ald_st%22%3A%221721700667113%22%2C%22recIndex%22%3A1%2C%22item_price%22%3A%226.3%22%2C%22ald_price_field%22%3A%22price%22%2C%22ump_invoke%22%3A0%2C%22x_item_ids%22%3A************%2C%22x_object_id%22%3A************%7D&xxc=promotionVenue',
        moduleJumpUrl:
          'https://pages.tmall.com/wow/a/act/miaosha/dailygroup/17887/18511/wupr?wh_pid=daily-533064&disableNav=YES&status_bar_transparent=true',
        currentAldResId: '33498784',
        moduleName: '百亿补贴 · 买贵必赔',
        shortTitle: '蜂花小麦蛋白护发素',
        type: 'item',
        title: '经典国货蜂花护发素小麦蚕丝改善干枯控油保湿女正品修复受损柔顺',
        distinctId: '************',
        price: '6.3',
        itemWhiteImg: '//img.alicdn.com/bao/upload/O1CN01A57gWw1EFyfXDcdUV_!!6000000000323-2-yinhe.png',
        moduleColor: '#FFEBEB',
        benefit: '15万人已抢',
        itemId: '************',
        dataSetId: '40105477',
        dataSetType: '2',
        __pos__: '1',
      },
      {
        clickUrl:
          '//item.taobao.com/item.htm?id=42770529509&fromChannel=bybtChannel&skuId=5149302687745&fpChannel=101&fpChannelSig=f53470cc485bf1cd2e850ffd00d8da2a5dcdd843&from=bybt&utparam=%7B%22x_object_type%22%3A%22item%22%2C%22ald_res%22%3A%2233498784%22%2C%22tpp_buckets%22%3A%22302%230%23273555%230_30636%230%23273555%230%22%2C%22floorId%22%3A38471623%2C%22ald_solution%22%3A%22commonPersonalizedSolution%22%2C%22pvid%22%3A%225019bd4c-1418-45b2-a63e-ff3d7a4331da%22%2C%22ald_biz%22%3A443%2C%22scm%22%3A%221007.10302.273555.38471623_0_0%22%2C%22traceId%22%3A%222150408e17217006657124340e3728%22%2C%22ald_st%22%3A%221721700667113%22%2C%22recIndex%22%3A2%2C%22item_price%22%3A%228.5%22%2C%22ald_price_field%22%3A%22price%22%2C%22ump_invoke%22%3A0%2C%22x_item_ids%22%3A42770529509%2C%22x_object_id%22%3A42770529509%7D&xxc=promotionVenue',
        moduleJumpUrl:
          'https://pages.tmall.com/wow/a/act/miaosha/dailygroup/17887/18511/wupr?wh_pid=daily-533064&disableNav=YES&status_bar_transparent=true',
        currentAldResId: '33498784',
        moduleName: '百亿补贴 · 买贵必赔',
        shortTitle: '团结220克老肥皂家用洗衣皂内衣皂婴儿皂真干净天然去污强',
        type: 'item',
        title: '团结220克老肥皂家用洗衣皂内衣皂婴儿皂真干净天然去污强',

        distinctId: '42770529509',
        price: '8.5',
        itemWhiteImg: '//img.alicdn.com/bao/upload/O1CN01IFz7Sd1q7Eaj7FAEt_!!6000000005448-0-yinhe.jpg',
        moduleColor: '#FFEBEB',
        benefit: '6万人已抢',
        itemId: '42770529509',
        dataSetId: '40105477',
        dataSetType: '2',
        __pos__: '2',
      },
      {
        clickUrl:
          '//item.taobao.com/item.htm?id=************&fromChannel=bybtChannel&skuId=5503103476057&fpChannel=101&fpChannelSig=afa8ca99d03f40038a605c284d0a91a93345eb14&from=bybt&utparam=%7B%22x_object_type%22%3A%22item%22%2C%22ald_res%22%3A%2233498784%22%2C%22tpp_buckets%22%3A%22302%230%23273555%230_30636%230%23273555%230%22%2C%22floorId%22%3A38471623%2C%22ald_solution%22%3A%22commonPersonalizedSolution%22%2C%22pvid%22%3A%225019bd4c-1418-45b2-a63e-ff3d7a4331da%22%2C%22ald_biz%22%3A443%2C%22scm%22%3A%221007.10302.273555.38471623_0_0%22%2C%22traceId%22%3A%222150408e17217006657124340e3728%22%2C%22ald_st%22%3A%221721700667113%22%2C%22recIndex%22%3A3%2C%22item_price%22%3A%226.71%22%2C%22ald_price_field%22%3A%22price%22%2C%22ump_invoke%22%3A0%2C%22x_item_ids%22%3A************%2C%22x_object_id%22%3A************%7D&xxc=promotionVenue',
        moduleJumpUrl:
          'https://pages.tmall.com/wow/a/act/miaosha/dailygroup/17887/18511/wupr?wh_pid=daily-533064&disableNav=YES&status_bar_transparent=true',
        currentAldResId: '33498784',
        moduleName: '百亿补贴 · 买贵必赔',
        shortTitle: '3m双面胶 强力加厚泡沫海绵无痕黏胶粘墙摆件3m胶带车家用 双面胶',
        type: 'item',
        title: '3m双面胶 强力加厚泡沫海绵无痕黏胶粘墙摆件3m胶带车家用 双面胶',

        distinctId: '************',
        price: '6.71',
        itemWhiteImg: '//img.alicdn.com/bao/upload/O1CN01l6NvUe1sYpp9W8JzL_!!6000000005779-0-yinhe.jpg',
        moduleColor: '#FFEBEB',
        benefit: '6万人已抢',
        itemId: '************',
        dataSetId: '40105477',
        dataSetType: '2',
        __pos__: '3',
      },
      {
        clickUrl: 'https://item.taobao.com/item.htm?id=730344766230&spm=a21y2.8291224.2879496.16.512d3569mNhAJg',
        moduleJumpUrl:
          'https://pages.tmall.com/wow/a/act/miaosha/dailygroup/17887/18511/wupr?wh_pid=daily-533064&disableNav=YES&status_bar_transparent=true',
        currentAldResId: '35105378',
        contentId: '17224890407190001',
        itemWhiteImg: 'https://img.alicdn.com/imgextra/i1/O1CN01q7rGNz1sSsfcuP0mg_!!6000000005766-0-tps-800-800.jpg',
        shortTitle: '农夫山泉饮用天然水550ml*12瓶',
        itemId: '730344766230',
        distinctId: '17224890407190001',
        dataSetId: 40771268,
        price: '14.9',
        id: '17224890407190001',
        __pos__: '4',
      },
    ],
    distinctId: '0',
    cardType: 'item_large',
    cardColor: 'var(--bg-light-color, #f5f5f5) ',
    cardTitle: '百亿补贴 · 买贵必赔',
    cardJumpUrl:
      'https://pages.tmall.com/wow/a/act/miaosha/dailygroup/17887/18511/wupr?wh_pid=daily-533064&disableNav=YES&status_bar_transparent=true',
    __pos__: 1,
    __track__: '34552784.34648458.44844655.5203.1',
  },
  {
    cardData: [
      {
        distinctId: 0,
        title: '#来波回忆杀# 一起找寻你和淘宝的珍贵记忆',
        jumpUrl: 'https://bbs.taobao.com/home.html',
        __pos__: 1,
      },
      {
        distinctId: 1,
        title: '#前辈请赐教# 一起聊聊生意经吧！',
        jumpUrl: 'https://bbs.taobao.com/home.html',
        __pos__: 2,
      },
      {
        distinctId: 2,
        title: '2024卖家之声论坛团队火热纳新优秀管理成员',
        jumpUrl: 'https://bbs.taobao.com/home.html',
        __pos__: 3,
      },
      {
        distinctId: 3,
        title: '淘宝21周岁啦！准点加购，获免单机会～',
        jumpUrl: 'https://bbs.taobao.com/home.html',
        __pos__: 4,
      },
      {
        distinctId: 4,
        title: '什么东西，看似价高，实则超值？',
        jumpUrl: 'https://bbs.taobao.com/home.html',
        __pos__: 5,
      },
    ],
    distinctId: 0,
    cardType: 'bbs',
    cardColor: 'var(--bg-light-color, #f5f5f5) ',
    cardTitle: '淘江湖',
    cardJumpUrl: 'https://jianghu.taobao.com/home.html',
    __pos__: 2,
  },
  {
    cardData: [
      {
        imgUrl: 'https://gw.alicdn.com/imgextra/i4/O1CN01Duo6dH1ZEhkWuzGLj_!!6000000003163-2-tps-240-240.png',
        liveExtUrl: '',
        title: '',
        liveHallUrl: 'https://tbzb.taobao.com',
        liveId: '',
        __pos__: 1,
        __track__: '34667251.34667251.44846497.5077.1',
      },
      {
        imgUrl: 'https://gw.alicdn.com/imgextra/i2/O1CN01bkQjie1lw0aRdh860_!!6000000004882-2-tps-240-240.png',
        liveExtUrl: '',
        title: '',
        liveHallUrl: 'https://tbzb.taobao.com',
        liveId: '',
        __pos__: 2,
        __track__: '34667251.34667251.44846497.5077.2',
      },
    ],
    cardType: 'live',
    cardColor: 'var(--bg-light-color, #f5f5f5) ',
    cardTitle: '直播间',
    cardJumpUrl: 'https://tbzb.taobao.com/',
    __pos__: 3,
  },
  {
    cardData: [
      {
        clickUrl:
          '//detail.tmall.com/item.htm?id=699730867287&scm=1007.10302.274556.37590004_0_0&pvid=5a0bb14d-8373-4176-9821-f48b70459260&utparam=%7B%22x_object_type%22%3A%22item%22%2C%22ald_res%22%3A%2233498786%22%2C%22tpp_buckets%22%3A%22302%230%23273555%230_30636%230%23273555%230%22%2C%22floorId%22%3A37590004%2C%22ald_solution%22%3A%22commonPersonalizedSolution%22%2C%22pvid%22%3A%225a0bb14d-8373-4176-9821-f48b70459260%22%2C%22ald_biz%22%3A443%2C%22scm%22%3A%221007.10302.273555.37590004_0_0%22%2C%22traceId%22%3A%222150408e17217006657124340e3728%22%2C%22ald_st%22%3A%221721700666078%22%2C%22recIndex%22%3A1%2C%22item_price%22%3A%224.5%22%2C%22ald_price_field%22%3A%22price%22%2C%22ump_invoke%22%3A0%2C%22x_item_ids%22%3A699730867287%2C%22x_object_id%22%3A699730867287%7D&xxc=promotionVenue',
        moduleJumpUrl:
          'https://huodong.taobao.com/wow/a/act/tmall/dailygroup/21201/21856/wupr?wh_pid=daily-532426&disableNav=YES&status_bar_transparent=true',
        currentAldResId: '33498786',
        moduleName: '淘工厂货',
        type: 'item',
        distinctId: '699730867287',
        price: '4.5',
        itemWhiteImg: '//img.alicdn.com/bao/upload/O1CN01g7JjP51paiSnOUTXe_!!6000000005377-2-yinhe.png',
        moduleColor: 'var(--bg-light-color, #f5f5f5) ',
        itemId: '699730867287',
        dataSetId: '39250066',
        dataSetType: '2',
        __pos__: '1',
      },
      {
        clickUrl:
          '//detail.tmall.com/item.htm?id=706755381987&scm=1007.10302.274556.37590004_0_0&pvid=5a0bb14d-8373-4176-9821-f48b70459260&utparam=%7B%22x_object_type%22%3A%22item%22%2C%22ald_res%22%3A%2233498786%22%2C%22tpp_buckets%22%3A%22302%230%23273555%230_30636%230%23273555%230%22%2C%22floorId%22%3A37590004%2C%22ald_solution%22%3A%22commonPersonalizedSolution%22%2C%22pvid%22%3A%225a0bb14d-8373-4176-9821-f48b70459260%22%2C%22ald_biz%22%3A443%2C%22scm%22%3A%221007.10302.273555.37590004_0_0%22%2C%22traceId%22%3A%222150408e17217006657124340e3728%22%2C%22ald_st%22%3A%221721700666078%22%2C%22recIndex%22%3A2%2C%22item_price%22%3A%226.18%22%2C%22ald_price_field%22%3A%22price%22%2C%22ump_invoke%22%3A0%2C%22x_item_ids%22%3A706755381987%2C%22x_object_id%22%3A706755381987%7D&xxc=promotionVenue',
        moduleJumpUrl:
          'https://huodong.taobao.com/wow/a/act/tmall/dailygroup/21201/21856/wupr?wh_pid=daily-532426&disableNav=YES&status_bar_transparent=true',
        currentAldResId: '33498786',
        moduleName: '淘工厂货',
        type: 'item',
        distinctId: '706755381987',
        price: '6.18',
        itemWhiteImg: '//img.alicdn.com/bao/upload/O1CN01smkWVp1yoIktwCWx8_!!6000000006625-2-yinhe.png',
        moduleColor: 'var(--bg-light-color, #f5f5f5) ',
        itemId: '706755381987',
        dataSetId: '39250066',
        dataSetType: '2',
        __pos__: '2',
      },
    ],
    cardType: 'item_small',
    cardColor: 'var(--bg-light-color, #f5f5f5) ',
    cardTitle: '淘工厂货',
    cardJumpUrl:
      'https://huodong.taobao.com/wow/a/act/tmall/dailygroup/21201/21856/wupr?wh_pid=daily-532426&disableNav=YES&status_bar_transparent=true',
    __pos__: 4,
  },
  {
    cardData: [
      {
        clickUrl:
          '//detail.tmall.com/item.htm?id=12428860408&scm=1007.10302.274556.38567666_0_0&pvid=de3dd4e7-8a65-4257-8cab-f82eda2bcae0&utparam=%7B%22x_object_type%22%3A%22item%22%2C%22ald_res%22%3A%2234756461%22%2C%22tpp_buckets%22%3A%22302%230%23273555%230_30636%230%23273555%230%22%2C%22floorId%22%3A38567666%2C%22ald_solution%22%3A%22commonPersonalizedSolution%22%2C%22pvid%22%3A%22de3dd4e7-8a65-4257-8cab-f82eda2bcae0%22%2C%22ald_biz%22%3A443%2C%22scm%22%3A%221007.10302.273555.38567666_0_0%22%2C%22traceId%22%3A%222150408e17219090608885290ee741%22%2C%22ald_st%22%3A%221721909061572%22%2C%22recIndex%22%3A1%2C%22item_price%22%3A%228.9%22%2C%22ald_price_field%22%3A%22price%22%2C%22ump_invoke%22%3A0%2C%22x_item_ids%22%3A12428860408%2C%22x_object_id%22%3A12428860408%7D&xxc=promotionVenue',
        type: 'item',
        utparam: {
          floorId: 38567666,
          recIndex: 1,
          x_object_type: 'item',
          pvid: 'de3dd4e7-8a65-4257-8cab-f82eda2bcae0',
          x_item_ids: 12428860408,
          scm: '1007.10302.273555.38567666_0_0',
          x_object_id: 12428860408,
          tpp_buckets: '302#0#273555#0_30636#0#273555#0',
        },
        cvrScore: '0.0000010',
        distinctId: '12428860408',
        trackParams: {
          trackInfo:
            '/sea.hangye.floor-tm-----scm=1007.10302.273555.38567666_0_0&pvid=de3dd4e7-8a65-4257-8cab-f82eda2bcae0&utLogMap=%7B%22recIndex%22%3A1%2C%22x_hestia_source%22%3A%22tm_fen_floor%22%2C%22x_object_type%22%3A%22item%22%2C%22algo_layer_ext%22%3A%22%22%2C%22calibrationScore%22%3A%22%22%2C%22x_hestia_subsource%22%3A%22default%22%2C%22wh_pid%22%3A-1%2C%22match_type_score%22%3A0.0%2C%22x_hestia_flow%22%3A38567666%2C%22x_hestia_rtp_biz_score%22%3A%22%22%2C%22tpp_buckets%22%3A%22302%230%23273555%230_30636%230%23273555%230%22%2C%22floorId%22%3A38567666%2C%22x_item_ids%22%3A12428860408%2C%22pvid%22%3A%22de3dd4e7-8a65-4257-8cab-f82eda2bcae0%22%2C%22x_algo_paras%22%3A%221007.10302.273555.38567666_0_0%3Ade3dd4e7-8a65-4257-8cab-f82eda2bcae0%3A0%3Atm_fen_floor%3A-1%3A%3A%3Aitem%3Apv%3A0%3Adefault-___12428860408%3A8%3A7810452.00000%3A0%3A38567666%3A0.00000%3A0.00000%3A8.84%3A7810452.00000%3A0.0%3A0%3A40%3A0%3A0%3A0-0-0-0%3A0-0-0-0-0-0-0-0-0-0-0-0-0-0-0%3A-0%3A45.00-3.00-0.00000-0.00000-0-1-1.00-0.00000%3Amock-0-0-0-0-0-0-0-0-0-0-0-0-0-0-0-0%3A%3AA0-B0-C0-D0-E0-0-0-0-0%22%2C%22scm%22%3A%221007.10302.273555.38567666_0_0%22%2C%22x_object_id%22%3A12428860408%7D-----GET',
        },
        price: '8.9',
        ctrScore: '0.0000010',
        __track__: '34756461.34756461.44945520.4984.1',
        itemWhiteImg: '//img.alicdn.com/bao/upload/O1CN0129rZaD1b0A7VUZFiU_!!6000000003402-2-yinhe.png',
        itemId: '12428860408',
        dataSetId: '40201159',
        dataSetType: '2',
        _mt_: {
          sc_item_img_800_800_transparent:
            'res:34756461;s:yinheItem;kmf:itemWhiteImg;mid:105146425113;kyh:sc_item_img_800_800_transparent;kef:transparentImg',
        },
        __pos__: 1,
      },
      {
        clickUrl:
          '//detail.tmall.com/item.htm?id=41048272115&scm=1007.10302.274556.38567666_0_0&pvid=de3dd4e7-8a65-4257-8cab-f82eda2bcae0&utparam=%7B%22x_object_type%22%3A%22item%22%2C%22ald_res%22%3A%2234756461%22%2C%22tpp_buckets%22%3A%22302%230%23273555%230_30636%230%23273555%230%22%2C%22floorId%22%3A38567666%2C%22ald_solution%22%3A%22commonPersonalizedSolution%22%2C%22pvid%22%3A%22de3dd4e7-8a65-4257-8cab-f82eda2bcae0%22%2C%22ald_biz%22%3A443%2C%22scm%22%3A%221007.10302.273555.38567666_0_0%22%2C%22traceId%22%3A%222150408e17219090608885290ee741%22%2C%22ald_st%22%3A%221721909061572%22%2C%22recIndex%22%3A2%2C%22item_price%22%3A%2229.9%22%2C%22ald_price_field%22%3A%22price%22%2C%22ump_invoke%22%3A0%2C%22x_item_ids%22%3A41048272115%2C%22x_object_id%22%3A41048272115%7D&xxc=promotionVenue',
        currentAldResId: '34756461',
        type: 'item',
        utparam: {
          floorId: 38567666,
          recIndex: 2,
          x_object_type: 'item',
          pvid: 'de3dd4e7-8a65-4257-8cab-f82eda2bcae0',
          x_item_ids: 41048272115,
          scm: '1007.10302.273555.38567666_0_0',
          x_object_id: 41048272115,
          tpp_buckets: '302#0#273555#0_30636#0#273555#0',
        },
        cvrScore: '0.0000010',
        distinctId: '41048272115',
        trackParams: {
          trackInfo:
            '/sea.hangye.floor-tm-----scm=1007.10302.273555.38567666_0_0&pvid=de3dd4e7-8a65-4257-8cab-f82eda2bcae0&utLogMap=%7B%22recIndex%22%3A2%2C%22x_hestia_source%22%3A%22tm_fen_floor%22%2C%22x_object_type%22%3A%22item%22%2C%22algo_layer_ext%22%3A%22%22%2C%22calibrationScore%22%3A%22%22%2C%22x_hestia_subsource%22%3A%22default%22%2C%22wh_pid%22%3A-1%2C%22match_type_score%22%3A0.0%2C%22x_hestia_flow%22%3A38567666%2C%22x_hestia_rtp_biz_score%22%3A%22%22%2C%22tpp_buckets%22%3A%22302%230%23273555%230_30636%230%23273555%230%22%2C%22floorId%22%3A38567666%2C%22x_item_ids%22%3A41048272115%2C%22pvid%22%3A%22de3dd4e7-8a65-4257-8cab-f82eda2bcae0%22%2C%22x_algo_paras%22%3A%221007.10302.273555.38567666_0_0%3Ade3dd4e7-8a65-4257-8cab-f82eda2bcae0%3A0%3Atm_fen_floor%3A-1%3A%3A%3Aitem%3Apv%3A0%3Adefault-___41048272115%3A8%3A2342564.00000%3A1%3A38567666%3A0.00000%3A0.00000%3A47.72%3A2342564.00000%3A0.0%3A0%3A59%3A0%3A0%3A0-0-0-0%3A0-0-0-0-0-0-0-0-0-0-0-0-0-0-0%3A-0%3A45.00-3.00-0.00000-0.00000-0-1-1.00-0.00000%3Amock-0-0-0-0-0-0-0-0-0-0-0-0-0-0-0-0%3A%3AA0-B0-C0-D0-E0-0-0-0-0%22%2C%22scm%22%3A%221007.10302.273555.38567666_0_0%22%2C%22x_object_id%22%3A41048272115%7D-----GET',
        },
        price: '29.9',
        ctrScore: '0.0000010',
        __track__: '34756461.34756461.44945520.4984.2',
        itemWhiteImg: '//img.alicdn.com/bao/upload/O1CN01Jd9GzI1U8DpF50U7t_!!6000000002472-2-yinhe.png',
        itemId: '41048272115',
        dataSetId: '40201159',
        dataSetType: '2',
        _mt_: {
          sc_item_img_800_800_transparent:
            'res:34756461;s:yinheItem;kmf:itemWhiteImg;mid:107439286095;kyh:sc_item_img_800_800_transparent;kef:transparentImg',
          itemWhiteImg:
            'res:34756461;s:yinheItem;kmf:itemWhiteImg;mid:108359184497;kyh:itemWhiteImg;kef:transparentImg',
        },
        __pos__: 2,
      },
    ],
    distinctId: 0,
    cardType: 'item_small',
    cardColor: 'var(--bg-light-color, #f5f5f5) ',
    cardTitle: '淘宝秒杀',
    cardJumpUrl:
      'https://huodong.taobao.com/wow/a/act/tao/dailygroup/22081/22784/wupr?wh_pid=daily-538275&disableNav=YES&status_bar_transparent=true',
    __pos__: 5,
    __track__: '34552784.34648461.44939722.5203.4',
  },
] as CardData[];

const getLocalCache = async (key: string) => {
  try {
    const aldConfig = window?.$coreAldMultiRequestConfig?.[key];
    if (aldConfig) {
      const res = await requestToLocalCache(aldConfig.data);
      return res?.data?.originResponse?.data || [];
    }
    return [];
  } catch (error) {
    return [];
  }
};
// let businessEntryCacheData: any = [];
// getLocalCache('BusinessEntry').then((res) => {
//   if (res?.length) {
//     businessEntryCacheData = res;
//   }
// });
export const useCardsData = (cards: CardData[]) => {
  const [cachedCards, setCachedCards] = useState<CardData[]>();
  useMemo(() => {
    getLocalCache('BusinessEntry').then((res) => {
      if (res?.length) {
        setCachedCards(res);
      }
    });
  }, []);

  return {
    cards,
    cachedCards: cachedCards,
    backupCards: backupdata,
  };
};