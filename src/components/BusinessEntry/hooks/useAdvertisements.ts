import { useMemo } from 'react';
import { $staticConfig } from '../../../utils/constant';
import { reportCustom } from '../../../utils/jstracker';
import { addQuery, getQuery } from '../../../utils/url';
import type { AdDetailNew, AldAdvertisement } from '../typings';
import { DataSource, DefaultScm } from './constants';

export function useAdvertisementData(aldAdvertisements: Nullable<AldAdvertisement[]>) {
  const dataAdFromZebra = Array.isArray($staticConfig?.bannerBackupNew)
    ? ($staticConfig.bannerBackupNew as AdDetailNew[])
        .filter(
          (dataItem) =>
            dataItem.title1 &&
            dataItem.jumpUrl &&
            ((dataItem.backgroundColor && dataItem.itemImage) || dataItem.backgroundImage),
        )
        .map((dataItem) => ({
          ...dataItem,
          source: DataSource.Zebra,
        }))
    : [];

  const advertisements = useMemo(() => {
    const dataAdFromAld: AdDetailNew[] = Array(5).fill(null);
    (aldAdvertisements || [])
      .filter(
        (dataItem) =>
          dataItem.title1 &&
          dataItem.jumpUrl &&
          ((dataItem.backgroundColor && dataItem.itemImage) || dataItem.backgroundImage),
      ).forEach((dataItem) => {
        const _pos = dataItem.__pos__ - 1;
        if (_pos < 0 || _pos > 4) return;
        dataAdFromAld[_pos] = {
          ...dataItem,
          source: DataSource.Ald,
        };
      });

    const resDataAd: AdDetailNew[] = [];

    for (let i = 0; i < 5; i++) {
      let curItem: AdDetailNew;
      let resScm = DefaultScm.Activity;
      if (dataAdFromAld[i]) {
        curItem = dataAdFromAld[i];
        resScm = DefaultScm.Activity;
      } else if (dataAdFromZebra[i]) {
        curItem = dataAdFromZebra[i];
        resScm = DefaultScm.Bottom;
      }

      // @ts-expect-error
      if (curItem) {
        let targetUrl = curItem?.jumpUrl;
        if (!getQuery('scm', curItem.jumpUrl)) {
          targetUrl = addQuery(
            {
              scm: resScm,
            },
            curItem.jumpUrl,
          );
        }
        if (!getQuery('custom_content_source', curItem.jumpUrl) && curItem.custom_content_source) {
          targetUrl = addQuery(
            {
              custom_content_source: curItem.custom_content_source,
            },
            curItem.jumpUrl,
          );
        }
        resDataAd.push({
          ...curItem,
          jumpUrl: targetUrl,
          scm: resScm,
        });
      }
    }

    if (!resDataAd.length) {
      backupData.forEach((dataItem) => {
        const resScm = DefaultScm.Bottom;
        let targetUrl = dataItem.jumpUrl;
        if (!getQuery('scm', dataItem.jumpUrl)) {
          targetUrl = addQuery(
            {
              scm: resScm,
            },
            dataItem.jumpUrl,
          );
        }
        resDataAd.push({
          ...dataItem,
          jumpUrl: targetUrl,
          scm: resScm,
        });
      });
    }

    if (!resDataAd.length) {
      reportCustom({
        code: 'empty-transformer-area',
        message: '[ad][empty]',
        sampling: 1,
        c1: JSON.stringify([...dataAdFromAld, ...dataAdFromZebra]),
      });
    }

    return resDataAd;
  }, [aldAdvertisements]);

  return { advertisements };
}

const backupData = [
  {
    topLogo: 'https://img.alicdn.com/imgextra/i4/O1CN01ADVUfc1DxCgZ6syBK_!!6000000000282-2-tps-140-32.png',
    titleColor: '#fff',
    title1: '淘宝秒杀',
    title2: '天天好价',
    subtitle1: '',
    itemImage: 'https://img.alicdn.com/imgextra/i3/O1CN01N2qtYz232HTtcxJAl_!!6000000007197-2-tps-480-384.png',
    backgroundColor: '#ee2929',
    backgroundImage: '',
    jumpUrl:
      'https://huodong.taobao.com/wow/a/act/tao/dailygroup/22081/22784/wupr?wh_pid=daily-538275&disableNav=YES&status_bar_transparent=true',
    source: DataSource.Zebra,
  },
] as AdDetailNew[];