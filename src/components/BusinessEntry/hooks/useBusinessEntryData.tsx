import { useEffect } from 'react';
import { RequestStatus } from '../../../contexts/CoreAldDataContext';
import useCoreAldDataContext from '../../../hooks/useCoreAldDataContext';
import { reportCustom } from '../../../utils/jstracker';
import type { AdDetailNew, CardData } from '../typings';
import { useAdvertisementData } from './useAdvertisements';
import { useCardsData } from './useCardsdata';

export function useBusinessEntryData() {
  const {
    data: cards = [],
    error: fetchCardsFailed,
    status: cardsStatus,
  } = useCoreAldDataContext<CardData[]>('BusinessEntry');
  const {
    data: aldAdvertisements,
    error: fetchAdvertisementsFailed,
    status: adStatus,
  } = useCoreAldDataContext<AdDetailNew[]>('SlideBanner');

  useEffect(() => {
    if (fetchCardsFailed) reportCustom({
        code: 'module-data',
        message: '[yingxiao][cards][error]',
        sampling: 1,
        c1: JSON.stringify(fetchCardsFailed),
      });
  }, [fetchCardsFailed]);

  useEffect(() => {
    if (fetchAdvertisementsFailed) reportCustom({
        code: 'module-data',
        message: '[yingxiao][ad][error]',
        sampling: 1,
        c1: JSON.stringify(fetchAdvertisementsFailed),
      });
  }, [fetchAdvertisementsFailed]);

  return {
    ...useAdvertisementData(aldAdvertisements),
    ...useCardsData(cards),

    loading: [cardsStatus, adStatus].some(
      (s) => s === RequestStatus.LOADING,
    ),
  };
}
