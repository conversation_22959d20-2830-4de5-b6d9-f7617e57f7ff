import type { CSSProperties } from 'react';

export interface IBaseProps {
  style?: CSSProperties;
  posId: number;
}

export interface IResponsiveLayoutItem {
  props: Omit<IBaseProps, 'posId' | 'key'>;
  posId: number;
}

export interface IResponsiveLayout {
  itemList: Array<IResponsiveLayoutItem>;
}

export const RESPONSIVE_LAYOUT_CONTENT_MAP: IResponsiveLayout = {
  itemList: [
    {
      posId: 0,
      props: {
        style: {},
      },
    },
    {
      posId: 1,
      props: {
        style: {
          backgroundColor: 'var(--bg-light-color, #f5f5f5)',
        },
      },
    },
    {
      posId: 2,
      props: {
        style: {
          backgroundColor: 'var(--bg-light-color, #f5f5f5)',
        },
      },
    },
    {
      posId: 3,
      props: {
        style: {
          backgroundColor: 'var(--bg-light-color, #f5f5f5)',
        },
      },
    },
    {
      posId: 4,
      props: {
        style: {
          backgroundColor: 'var(--bg-light-color, #f5f5f5)',
        },
      },
    },
    {
      posId: 5,
      props: {
        style: {
          backgroundColor: 'var(--bg-light-color, #f5f5f5)',
        },
      },
    },
  ],
};
