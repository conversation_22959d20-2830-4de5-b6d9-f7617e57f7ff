import { memo, useMemo } from 'react';
import './index.less';
import BbsCard from './components/BbsCard';
import LiveCard from './components/LiveCard';
// import ItemCard from './components/ItemCard';
import { useBusinessEntryData } from './hooks/useBusinessEntryData';
import { RESPONSIVE_LAYOUT_CONTENT_MAP } from './LayoutMap';
import { getValidLengthCardData } from './lib/card';
import type { CardData } from './typings';
import type { IResponsiveLayoutItem, IBaseProps } from './LayoutMap';
import ClientHomeTaocoinEnter from '@ali/client-taocoin-enter';
import { mtopRequest } from '../../utils/mtop';
import AdaptiveGrid, { SmallBlock } from '../AdaptiveGrid';
import { goldLogBusinessEntryClk, goldLogBusinessEntryExp } from './lib/tracker';
import { AppearWeb } from '@ali/appear';
import { EvoProvider, RequestStatus } from '@/contexts/EvoContext';
import UserInfoNew from '@/components/UserInfoNew';
import ItemCard from './components/ItemCard';

const DefaultSpmC = 'yingxiao';

export interface IBaseCardProps extends IBaseProps {
  aldData?: CardData;
  cachedData?: CardData;
  zebraData?: CardData;
  backupData?: CardData;
  cardType?: 'item_small' | 'item_large';
}

const BusinessEntry = () => {
  const { cards, cachedCards, backupCards } = useBusinessEntryData();

  // 动态构建taskList，search和guang固定存在
  const taskList = useMemo(() => {
    const dynamicTasks = ['search', 'guang'];

    // 检查是否有bbs类型的卡片
    const hasBbsCard = [...(cards || []), ...(backupCards || [])]
      .some(card => card.cardType === 'bbs');
    if (hasBbsCard) dynamicTasks.push('bbs');

    // 检查是否有live类型的卡片
    const hasLiveCard = [...(cards || []), ...(backupCards || [])]
      .some(card => card.cardType === 'live');
    if (hasLiveCard) dynamicTasks.push('live');

    return dynamicTasks;
  }, [cards, backupCards]);

  const renderBusinessCards = (item: IResponsiveLayoutItem, index: number) => {
    const commonProps: IBaseProps = {
      ...item?.props,
      posId: item?.posId,
      style: {
        ...item?.props.style,
      },
    };
    if (item?.posId === 4) {
      return (
        <AppearWeb
          onFirstAppear={() => {
            goldLogBusinessEntryExp(item.posId, 'taocoin');
          }}
        >
          <div
            key={index}
            className="client-tao-coin-wrapper"
            {...commonProps}
            data-spm={DefaultSpmC}
            onClick={() => {
              goldLogBusinessEntryClk(item.posId, 'taocoin');
            }}
          >
            <ClientHomeTaocoinEnter
              mtopRequest={mtopRequest}
              taskList={taskList}
            />
          </div>
        </AppearWeb>
      );
    }
    if (item?.posId === 5) {
      return (<EvoProvider
        pageCode="home"
        moduleCode="homeRight"
        childRender={(value) => {
          if (value.status === RequestStatus.LOADING) {
              return <SmallBlock />;
            } else {
              return <UserInfoNew />;
            }
        }}
      />);
    }
    const currentCard = cards?.find?.((data) => data.__pos__ === item.posId);
    const currentCachedCard = cachedCards?.find?.(
      (data) => data.__pos__ === item.posId,
    );
    const currentZebraCard = {} as any;
    const currentBackupCard = backupCards?.find((data) => data.__pos__ === item.posId);
    // console.log(
    //   'currentCard, currentCachedCard=',
    //   currentCard,
    //   currentCachedCard,
    // );
    const currentType = getValidLengthCardData({
      card: currentCard,
      cachedCard: currentCachedCard,
      zebraCard: currentZebraCard,
      backupCard: currentBackupCard,
      length: 2,
    })?.cardType;
    switch (currentType) {
      case 'bbs':
        return (
          <BbsCard
            key={index}
            {...commonProps}
            aldData={currentCard}
            cachedData={currentCachedCard}
            zebraData={currentZebraCard}
            backupData={currentBackupCard}
          />
        );
      case 'live':
        return (
          <LiveCard
            key={index}
            {...commonProps}
            aldData={currentCard}
            cachedData={currentCachedCard}
            zebraData={currentZebraCard}
            backupData={currentBackupCard}
          />
        );
      // case 'item_large':
      //   return (
      //     <ItemCard
      //       key={index}
      //       {...commonProps}
      //       aldData={currentCard}
      //       cachedData={currentCachedCard}
      //       zebraData={currentZebraCard}
      //       backupData={currentBackupCard}
      //       cardType={'item_large'}
      //     />
      //   );
      case 'item_small':
      default:
        return (<ItemCard
          key={index}
          {...commonProps}
          aldData={currentCard}
          cachedData={currentCachedCard}
          zebraData={currentZebraCard}
          backupData={currentBackupCard}
          cardType={'item_small'}
        />);
    }
  };

  return (
    <AdaptiveGrid>
      {[2, 3, 4, 5].map((item: number) => {
        return (
          <SmallBlock key={item}>
            {renderBusinessCards(
              RESPONSIVE_LAYOUT_CONTENT_MAP.itemList[item],
              item,
            )}
          </SmallBlock>
        );
      })}
    </AdaptiveGrid>
  );
};

export default memo(BusinessEntry);
