import type { CardData } from '../typings';

interface FuncParams {
  card?: CardData;
  cachedCard?: CardData;
  zebraCard?: CardData;
  backupCard?: CardData;
  length: number;
}

export function getValidLengthCardData({
  card,
  cachedCard,
  zebraCard,
  backupCard,
  length,
}: FuncParams) {
  if (card?.cardData && card?.cardData?.length >= length) {
    return card;
  } else if (cachedCard?.cardData && cachedCard?.cardData?.length >= length) {
    return cachedCard;
  } else if (zebraCard?.cardData && zebraCard?.cardData?.length >= length) {
    return zebraCard;
  } else if (backupCard?.cardData && backupCard?.cardData?.length >= length) {
    return backupCard;
  }
  return backupCard;
}
