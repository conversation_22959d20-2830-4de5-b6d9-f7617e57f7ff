import { goldlogRecord } from '../../../utils/goldlog';
import { getPageSpm } from '../../../utils/spm';

export const goldLogBusinessEntryExp = (
  index: string | number | bigint,
  contentType: string,
  otherParams: any = {},
) => {
  goldlogRecord({
    logKey: '/tbpcclient.newpc.marketmodule',
    gmKey: 'EXP',
    goKey: {
      spm: getPageSpm('yingxiao', `d${index}`),
      contentType,
      ...otherParams,
    },
  });
};

export const goldLogBusinessEntryClk = (
  index: string | number | bigint,
  contentType: string,
  otherParams: any = {},
) => {
  goldlogRecord({
    logKey: '/tbpcclient.newpc.marketmodule',
    gmKey: 'CLK',
    goKey: {
      spm: getPageSpm('yingxiao', `d${index}`),
      contentType,
      ...otherParams,
    },
  });
};
