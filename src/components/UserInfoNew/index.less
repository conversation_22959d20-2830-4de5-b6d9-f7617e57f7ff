.new-user-wrapper {
    overflow: hidden;
    box-sizing: border-box;
    width: 100%;
    height: 100%;
    border-radius: 8px;

    .member-wrapper {
        display: block;
        background: var(--tbpc-non-vip-bg, #fff1eb);
    }

    .member-wrapper-with-config {
        display: block;
        background: var(--tbpc-info-bg-color,
                radial-gradient(142% 156% at 12% 0%,
                    #fdf4e9 0%,
                    rgba(253, 244, 233, 0) 100%),
                #FFE8C7;
            );
    }

    .member-bd-left {
        display: flex;
        flex-direction: row;
        flex-wrap: nowrap;
        align-items: center;
        width: 100%;
        height: 32px;

        .member-avatar-tag {
            width: 36px;
            height: 16px;
            margin-left: 8px;
        }
        .member-avatar-tag-bg {
            width: 36px;
            height: 16px;
            margin-left: 8px;
            background-repeat: no-repeat;
            background-size: contain;
            background-image: var(--tbpc-vip-icon, url(https://img.alicdn.com/imgextra/i1/O1CN01OY5LDQ1QrKgGtUfVZ_!!6000000002029-2-tps-72-32.png));
        }
    }

    .member-bd-right {
        display: flex;
        flex-direction: row;
        flex-wrap: nowrap;
        align-items: center;
        justify-content: end;
        width: 150px;
        height: 22px;
        color: var(--tbpc-primary-color, rgba(0, 0, 0, 0.92));
        cursor: pointer;
        position: relative;

        .benefit-text {
            font-size: 14px;
            line-height: 22px;
        }

        .benefit-text span {
            font-weight: 400;
        }

        .arrow-icon {
            transform: rotate(270deg);
            margin-left: 4px;
            object-fit: contain;
            font-size: 8px;
            color: var(--tbpc-primary-color, rgba(0, 0, 0, 0.92));
        }

        .benefit-background {
            width: 247px;
            height: 95px;
            position: absolute;
            right: 0px;
            top: -1px;
            z-index: 1;
            background-image: var(--tbpc-vip-bg-image, url(https://img.alicdn.com/imgextra/i1/6000000005107/O1CN01eAvtcz1nb3lhpQ1ZV_!!6000000005107-2-gg_dtc.png));
            background-size: contain;
            background-repeat: no-repeat;
            background-position: center;
        }
    }

    .member-bd {
        display: flex;
        flex-direction: row;
        flex-wrap: nowrap;
        box-sizing: border-box;
        padding: 0 16px 0 8px;
        justify-content: space-between;
        align-items: center;
        width: 100%;
        height: 48px;
        box-sizing: border-box;

        // .member-nick {
        //     display: none;
        // }

        // &.member-bd-login {
        //     text-align: center;
        //     // margin: 4px;

        //     .member-nick-info {
        //         strong {
        //             display: inline-block;
        //             overflow: hidden;

        //             white-space: nowrap;
        //             text-overflow: ellipsis;

        //             color: var(--primary-color, #1f1f1f);

        //             font-size: 16px;

        //             &.member-nick {
        //                 max-width: 160px;
        //             }
        //         }

        //         .member-login-days {
        //             display: block;

        //             height: 16px;
        //             margin-top: 2px;

        //             color: var(--tbpc-tertiary-alpha-color, rgba(0, 0, 0, 0.4));

        //             font-size: 12px;
        //             line-height: 16px;
        //         }
        //     }

        //     .member-login-btn-container {
        //         display: block;
        //     }

        //     &.member-bd-horiz {
        //         .member-login-btn-container {
        //             float: left;
        //             line-height: 16px;
        //             margin-left: 1px;
        //         }
        //     }
        // }

        .member-nick-info {
            font-size: 16px;
            font-weight: 600;
            line-height: 24px;
            color: var(--tbpc-primary-color, rgba(0, 0, 0, 0.92));
        }

        // .btn-register,
        // .btn-open {
        //     margin-bottom: 4px;

        //     color: var(--primary-color, #1f1f1f);

        //     font-size: 12px;
        //     line-height: 12px;

        //     &:hover {
        //         color: var(--tb-brand-light-color, #ff5000);
        //     }
        // }

        // .divider {
        //     display: inline-block;

        //     width: 1px;
        //     height: 8px;
        //     margin-right: 8px;
        //     margin-left: 8px;

        //     background-color: rgba(0, 0, 0, 0.08);
        // }

        // .btn-register {
        //     margin-left: 12px;
        // }

        .avatar-wrapper {
            width: 32px;
            height: 32px;
            border-radius: 50%;
        }

        .avatar-wrapper-with-config {
            background-color: var(--tbpc-vip-color, #352a20);
        }

        .member-home {
            position: relative;
            overflow: hidden;
            width: 32px;
            height: 32px;
            border-radius: 50%;
            display: block;
        }

        .member-avatar {
            display: inline-block;

            box-sizing: border-box;
            width: 32px;
            height: 32px;

            border-radius: 50%;
            background-color: var(--bg-color, #fff);
            background-repeat: no-repeat;
            background-position: center;
            background-size: cover;
        }
    }

    .member-nickurl {
        font-size: 16px;
        font-weight: 600;
        line-height: 24px;
        color: var(--tbpc-primary-color, rgba(0, 0, 0, 0.92));
        margin-left: 8px;
        text-overflow: ellipsis;
        overflow: hidden;
        white-space: nowrap;
    }

    .member-avatar-order {
        width: 48px;
        height: 48px;
    }

    .member-nick-order {
        display: block;
        overflow: hidden;

        width: 144px;

        white-space: nowrap;
        text-overflow: ellipsis;
    }

    .member-nick-info-order {
        margin-top: 0;

        text-align: left;
    }

    .member-ft {
        overflow: hidden;

        .btn-user-not-login {
            position: absolute;

            width: 224px;
            height: 302px;
            margin-top: 2px;
            margin-right: 0;
            margin-left: 4px;

            color: var(--bg-color, #fff);
            border-radius: 12px;
            background-repeat: no-repeat;
            background-position: center;
            background-size: contain;

            font-weight: normal;

            .user-not-login-wrapper {
                width: 100%;
                height: 100%;
            }

            .user-not-login-rule {
                position: absolute;
                right: 0;
                bottom: 24px;
                left: 0;

                width: 80px;
                height: 22px;
                margin: 0 auto;
                padding-top: 2px;
                padding-right: 2px;

                line-height: 16px;
            }

            .user-not-login-rule:hover {
                background-color: rgba(255, 255, 255, 0.2);
            }

            .user-not-login-rule-question {
                width: 16px;
                height: 16px;

                vertical-align: middle;
            }

            .user-not-login-rule-title {
                vertical-align: middle;

                color: var(--bg-color, #fff);

                font-size: 12px;
                font-weight: normal;
                line-height: 12px;
            }
        }
    }

    .user-mytao {
        position: relative;
        display: flex;
        flex-direction: row;

        margin-top: 8px;

        .mytao-content {
            width: 58px;
            height: 46px;
            text-align: center;

            a {
                display: inline-block;
                width: 100%;
                color: var(--primary-color, #1f1f1f);
            }

            p {
                white-space: nowrap;

                color: var(--primary-color, #1f1f1f);

                line-height: 16px;
            }

            a:hover,
            a:hover p {
                cursor: pointer;

                color: var(--tb-brand-light-color, #ff5000);
            }
        }

        .mytao-icon {
            margin-bottom: 2px;

            text-align: center;

            font-size: 24px;
            line-height: 24px;
            padding: 2px;
        }

        .mytao-cart-count {
            height: 14px;
            line-height: 14px;
            min-width: 14px;
            padding: 0 3px;
            position: absolute;
            top: -2px;
            left: 30px;
            font-size: 10px;
            font-family: inter;
            font-weight: 500;
            background-color: var(--tb-brand-light-color, #ff5000);
            color: var(--bg-color, #fff);
            border-radius: 1000px;
            white-space: nowrap;
            box-sizing: border-box;
        }
    }

    .user-tipmain {
        position: relative;

        overflow: hidden;

        width: 252px;
        height: 18px;
        margin: 36px 0 0 30px;
    }

    .user-tip {
        overflow: hidden;

        width: 252px;
        height: 18px;

        .user-mod {
            position: relative;

            overflow: hidden;

            width: 252px;
            height: 18px;
        }

        .notice-tip {
            float: left;

            width: 50px;
            height: 18px;

            text-align: center;

            color: var(--tb-brand-light-color, #ff5000);
            border-radius: 2px;
            background-color: #ffefef;

            font-size: 12px;
            line-height: 18px;
        }

        p {
            float: left;
            overflow: hidden;

            width: 198px;
            margin-left: 4px;

            white-space: nowrap;
            text-overflow: ellipsis;

            color: #666;

            font-size: 12px;
            line-height: 18px;
        }
    }

    // 用户订单样式
    .user-orderMain {
        position: relative;

        overflow: hidden;

        width: 240px;
        height: 48px;
        margin-top: 16px;

        -webkit-border-radius: 6px;
        -moz-border-radius: 6px;
        border-radius: 6px;
        background-color: var(--tbpc-bg2-color, #f5f5f5);
    }

    .user-externalLink {
        height: 128px;
        margin-top: 8px;
        // margin-top: 4px;
        // background-image: linear-gradient(180deg, #F4F7F9 0%, #FFFFFF 100%);

        -webkit-border-radius: 12px;
        -moz-border-radius: 12px;
        border-radius: 12px;

        .user-externalLink-item {
            display: block;
            float: left;

            width: 58px;
            height: 48px;
            margin: 12px 0 8px;

            text-align: center;

            &:hover,
            &:hover .user-externalLink-item-text {
                cursor: pointer;

                color: var(--tb-brand-light-color, #ff5000);
            }

            .user-externalLink-item-img {
                width: 32px;
                height: 32px;

                background-size: 100% 100%;
            }

            .user-externalLink-item-text {
                overflow: hidden;

                width: 58px;
                height: 14px;
                margin-top: 4px;

                white-space: nowrap;
                text-overflow: ellipsis;

                color: var(--primary-color, #1f1f1f);

                line-height: 12px;
            }
        }

        .user-externalLink-slider-container-small {
            width: 224px !important;
            height: 70px !important;
            margin-left: 4px !important;
        }

        .user-externalLink-slider-container {
            overflow: hidden;

            width: 100%;
            height: 128px;
            margin-top: 16px;

            -webkit-border-radius: 12px;
            -moz-border-radius: 12px;
            border-radius: 8px;

            a {
                width: 100%;
                height: 100%;
            }

            .user-externalLink-slider-item {
                display: block;
                overflow: hidden;

                border-radius: 8px;
                background-repeat: no-repeat;
                background-position: center;
                background-size: cover;
            }
        }

        .slick-slider {
            button {
                display: block;

                margin: 0;
                padding: 0;

                cursor: pointer;

                color: transparent;
                border: none;
                outline: none;
                background-color: transparent;

                font-size: 0;
            }

            .slick-dots {
                position: absolute;
                z-index: 9;
                bottom: 8px;

                display: block;

                width: 100%;
                margin: 0;
                padding: 0;

                list-style: none;

                text-align: center;

                font-size: 0;

                li {
                    position: relative;

                    display: inline-block;
                    overflow: hidden;

                    width: 4px;
                    height: 4px;
                    margin: 0 4px;
                    padding: 0;

                    transition: width 0.2s linear;

                    border: 1px solid var(--tbpc-bg1-alpha-color, rgba(0, 0, 0, 0.04));
                    border-radius: 6px;
                    background-color: rgba(255, 255, 255, 0.6);

                    &.slick-active {
                        width: 8px;

                        background-color: var(--bg-color, #fff);
                    }
                }

                button {
                    width: 100%;
                    height: 100%;
                }
            }

            &:hover {

                .slick-prev,
                .slick-next {
                    display: block !important;
                }
            }

            .slick-prev,
            .slick-next {
                position: absolute;
                z-index: 9;
                top: 50%;

                display: none !important;

                width: 32px;
                height: 32px;

                -webkit-transform: translate(0, -50%);
                -ms-transform: translate(0, -50%);
                transform: translate(0, -50%);

                background-repeat: no-repeat;
                background-position: center;
                background-size: contain;
            }

            .slick-prev {
                left: 0;

                background-image: url(https://gw.alicdn.com/imgextra/i1/O1CN01WnpSnB1DqKgkqjvAy_!!6000000000267-2-tps-64-64.png);
            }

            .slick-next {
                right: 0;

                background-image: url(https://gw.alicdn.com/imgextra/i3/O1CN01LYDfty1FQLRndh0vh_!!6000000000481-2-tps-64-64.png);
            }

            &.user-ad-banner-single {

                .slick-dots,
                .slick-prev,
                .slick-next {
                    display: none !important;
                }
            }
        }
    }
}

.my-tao-container {
    border-radius: 12px;
    background: var(--bg-light-color, #f5f5f5);
    display: flex;
    flex-direction: column;
    flex-wrap: nowrap;
    width: 100%;
    height: 100%;
    cursor: pointer;

    .header {
        display: flex;
        flex-direction: row;
        flex-wrap: nowrap;
        box-sizing: border-box;
        padding: 12px 16px;
        align-items: center;
        width: 100%;
        height: 48px;
    }

    .titleWrapper {
        display: flex;
        flex-direction: row;
        flex-wrap: nowrap;
        align-items: center;
        width: 64px;
        height: 24px;
    }

    .title {
        font-size: 16px;
        font-weight: 600;
        line-height: 24px;
        color: var(--tbpc-primary-color, rgba(0, 0, 0, 0.92));
    }

    .member-column {
        display: flex;
        flex-direction: row;
        justify-content: space-around;
        align-items: center;
        flex-wrap: nowrap;
        box-sizing: border-box;
        padding: 12px 16px 8px 16px;
        width: 100%;
        height: 64px;

        a {
            display: flex;
            flex-direction: column;
            flex-wrap: nowrap;
            box-sizing: border-box;
            padding: 0px 10px;
            justify-content: center;
            align-items: center;
            width: 92px;
            height: 44px;
            color: var(--tbpc-primary-color, rgba(0, 0, 0, 0.92));
        }

        a:hover {
            cursor: pointer;
            color: var(--tbpc-client-hover-color, #ff5000);
        }
        a:active {
            cursor: pointer;
            color: var(--tbpc-client-active-color, #FFAD87);
        }
        .stat-number {
            font-size: 24px;
            line-height: 24px;
            height: 24px;
            font-weight: 600;
            letter-spacing: -0.02em;
        }

        .stat-label {
            font-size: 12px;
            line-height: 16px;
            text-align: center;
            width: 36px;
            margin-top: 4px;
        }
    }
}