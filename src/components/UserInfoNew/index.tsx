import { useEffect, useMemo, useState } from 'react';
import './index.less';
import { isTrue, transferStringToFix2 } from '@/utils/index';
import { getAvatar, getNick, isLogin } from '@/utils/cookie';
import { getHello, goldLogUserExp, reportLog } from './utils';
import UserLogout from './components/UserLogout';
import {
  queryBag,
  queryBenefitCount,
  queryTaoCoinCount,
} from './services';
import {
  BENEFIT_AD_DATA,
  DEFAULT_BENEFIT_COUNT,
  DEFAULT_BENEFIT_RED_PACKET_JUMP_URL,
  DEFAULT_BENEFIT_COUPON_JUMP_URL,
  DefaultBottomColor,
  DefaultTopColor,
  DEFAULT_BENEFIT_JUMP_URL,
} from './constant';
import type {
  IAvatarConfig,
  IBenefitRemoteSlider,
  IBenefitBottomSlider,
  ILoginAreaConfig,
  IUserInfo,
} from './types';
import { LOGIN_STATUS } from './types';
import useEvoContext from '../../hooks/useEvoContext';
import { IEvoContextValue } from '../../contexts/EvoContext';
import { allSettled } from '@/utils/promise-all-settled';
import { getValueFromEvoData } from '@/utils/evo';
import LoginCard from '../LoginCard';

function UserInfo() {
  const { components } = useEvoContext('homeRight') as IEvoContextValue;
  // 是否登录
  const [login, setLogin] = useState<LOGIN_STATUS>(LOGIN_STATUS.NONE);
  const [loginAreaConfig, setLoginAreaConfig] = useState<ILoginAreaConfig>({
    topColor: DefaultTopColor,
    bottomColor: DefaultBottomColor,
  });
  // 昵称/头像/问候词
  const [userInfo] = useState<IUserInfo>({
    nick: getNick(),
    avatar: getAvatar(),
    helloText: isLogin() ? `${getHello()}，` : `${getHello()}`,
  });
  // 头像框
  const [avatarConfig, setAvatarConfig] = useState<IAvatarConfig>();
  // 个人专属权益
  const [benefitCount, setBenefitCount] = useState({
    isFetching: true,
    ...DEFAULT_BENEFIT_COUNT,
  });
  const [benefitSliderList, setBenefitSliderList] = useState<
    (IBenefitBottomSlider | IBenefitRemoteSlider)[]
  >([]);
  // 权益轮播数据请求状态
  const [isBenefitSliderLoading, setIsBenefitSliderLoading] = useState(true);

  useMemo(() => {
    if (isTrue(isLogin())) {
      setLogin(LOGIN_STATUS.WEAK);
    }
    if (typeof window?.isLoginFromLibPromise?.then === 'function') {
      window.isLoginFromLibPromise.then((res) => {
        if (res && res?.data) {
          setLogin(LOGIN_STATUS.STRONG);
        } else {
          setLogin(LOGIN_STATUS.NONE);
        }
      });
    }
  }, []);

  useEffect(() => {
    queryLoginArea();
  }, []);

  // 进行个人中心所有的请求
  async function queryLoginArea() {
    try {
      const _promiseList: any[] = [
        queryBag(),
      ] as const;
      const [bagRes] = await allSettled(_promiseList);
      let resLoginAreaConfig: ILoginAreaConfig = {};
      let resAvatarConfig: IAvatarConfig = {};
      let remindsList: IBenefitRemoteSlider[] = [];
      const configRes = getValueFromEvoData({
        components,
        componentCode: 'userInfo',
        blockCode: 'userInfoConfig',
      });
      if (configRes) {
        // 背景色处理
        resLoginAreaConfig = configRes?.[0] || {};
        if (resLoginAreaConfig?.topColor && resLoginAreaConfig?.bottomColor) {
          setLoginAreaConfig({
            topColor: resLoginAreaConfig.topColor,
            bottomColor: resLoginAreaConfig.bottomColor,
          });
        }
        // 头像框处理
        resAvatarConfig = configRes?.[0] || {};
      }
      const remindsRes = getValueFromEvoData({
        components,
        componentCode: 'assetInfo',
        blockCode: 'reminds',
      });
      if (remindsRes) {
        remindsList = remindsRes || [];
      }
      if (bagRes?.status === 'fulfilled') {
        goldLogUserExp('profile', 'nick');
        setLogin(LOGIN_STATUS.STRONG);
        // 处理头像框
        avatarRender(resAvatarConfig);
        // 处理营销入口
        benefitSliderRender({
          remindsList: remindsList,
        });
        // 红包/优惠券/淘金币
        benefitCountRender();
      } else {
        benefitSliderRender({
          remindsList: [],
        });
        setBenefitCount({
          isFetching: false,
          ...DEFAULT_BENEFIT_COUNT,
        });
      }
    } catch (e) {
      benefitSliderRender({
        remindsList: [],
      });
      setBenefitCount({
        isFetching: false,
        ...DEFAULT_BENEFIT_COUNT,
      });
      reportLog(`主接口异常, error: ${JSON.stringify(e)}`);
    }
  }

  function getBenefitCount<T>(
    response: { value?: { success: boolean; data: T } },
    key: keyof T,
    defaultCount: { success: boolean; num: number },
    shouldConvertToFix2: boolean = false,
  ) {
    return isTrue(response?.value?.success)
      ? {
        success: true,
        num: shouldConvertToFix2
          ? transferStringToFix2(
            (response?.value?.data?.[key] || 0) as string,
          )
          : response?.value?.data?.[key] || 0,
      }
      : defaultCount;
  }

  // 红包/优惠券/淘金币
  async function benefitCountRender() {
    try {
      const [redEnvelopeRes, taoCoinRes] = await allSettled([
        queryBenefitCount(),
        queryTaoCoinCount(),
      ]);
      const resBenefitCount = {
        isFetching: false,
        redEnvelop: getBenefitCount(
          redEnvelopeRes,
          'redEnvelope',
          DEFAULT_BENEFIT_COUNT.redEnvelop,
        ),
        coupon: getBenefitCount(
          redEnvelopeRes,
          'coupon',
          DEFAULT_BENEFIT_COUNT.coupon,
        ),
        taoCoin: getBenefitCount(
          taoCoinRes,
          'coinSaving',
          DEFAULT_BENEFIT_COUNT.taoCoin,
          true,
        ),
      };
      setBenefitCount(resBenefitCount);
    } catch (e) {
      setBenefitCount({
        isFetching: false,
        ...DEFAULT_BENEFIT_COUNT,
      });
    }
  }

  function avatarRender(resAvatarConfig: IAvatarConfig) {
    const { bgColor: resBgColor, iconUrl: resIconUrl } = resAvatarConfig;
    if (resBgColor && resIconUrl) {
      setAvatarConfig({
        bgColor: resBgColor,
        iconUrl: resIconUrl,
      });
    }
  }

  function generateBenefitUrl(slider: IBenefitRemoteSlider) {
    if (slider.jumpUrl) {
      return slider.jumpUrl;
    }
    return slider?.assetType === 'coupon'
      ? slider?.subAssetType === 'zk'
        ? DEFAULT_BENEFIT_JUMP_URL
        : `${DEFAULT_BENEFIT_COUPON_JUMP_URL}&g_couponId=${slider?.extraData?.templateCode || ''}&g_couponGroupId=${slider?.extraData?.couponTag || ''}`
      : DEFAULT_BENEFIT_RED_PACKET_JUMP_URL;
  }

  // 登录后权益模块兜底
  async function benefitSliderRender(params: {
    remindsList: IBenefitRemoteSlider[];
  }) {
    const { remindsList } = params;
    try {
      if (remindsList.length > 0) {
        const benefitListWithUrl = [...remindsList].map((slider) => {
          if (
            slider?.assetType &&
            ['coupon', 'fund'].includes(slider.assetType)
          ) {
            return {
              ...slider,
              targetUrl: generateBenefitUrl(slider),
              type: slider?.assetType === 'coupon' ? 'coupon' : 'fund',
            };
          } else {
            return {
              ...BENEFIT_AD_DATA[0],
              ...slider,
              type: 'ad',
            };
          }
        });
        // 先展示红包再展示卡券
        setBenefitSliderList(benefitListWithUrl);
        setIsBenefitSliderLoading(false);
      } else {
        let resBenefitAdList: IBenefitBottomSlider[] = [];
        resBenefitAdList = BENEFIT_AD_DATA;
        const adSlidersWithType = resBenefitAdList.map((slider) => ({
          ...slider,
          type: 'ad',
        }));
        setBenefitSliderList(adSlidersWithType);
        setIsBenefitSliderLoading(false);
      }
    } catch (e) {
      setIsBenefitSliderLoading(false);
    }
  }

  if (isBenefitSliderLoading) {
    return null;
  }

  return login === LOGIN_STATUS.NONE ? <LoginCard helloText={userInfo?.helloText} />
    : <div className="new-user-wrapper" data-spm="profile">
      <UserLogout
        login={login}
        loginAreaConfig={loginAreaConfig}
        userInfo={userInfo}
        avatarConfig={avatarConfig}
        showOrder
        benefitSliderList={benefitSliderList}
        benefitCount={benefitCount}
      />
    </div>;
}

export default UserInfo;
