import { memo, useMemo, useRef } from 'react';
import type { Settings as SliderSettings } from 'react-slick';
import Slider from '../../../Slick/slider.js';
import 'slick-carousel/slick/slick.css';
import './index.less';
import type { IOrderSliderItem } from '../../types';
import { SLIDER_ITEM_TYPE } from '../../types';
import { goldLogMyTaoClk, goldLogMyTaoExp } from '../../utils';

const DefaultSliderSettings: SliderSettings = {
  dots: false,
  infinite: true,
  autoplay: true,
  autoplaySpeed: 5000,
  arrows: false,
};

const spmKey = {
  [SLIDER_ITEM_TYPE.WaitPaymentInfo]: 'nonpayment',
  [SLIDER_ITEM_TYPE.LogisticsDeliveryInfo]: 'delivery',
  [SLIDER_ITEM_TYPE.LowPrice]: 'jiangjia',
  [SLIDER_ITEM_TYPE.AdvInfo]: 'yingxiao',
  [SLIDER_ITEM_TYPE.BackupInfo]: 'backup',
};

function UserSlider(props: IUserSlider) {
  const { isFetching = true, data: orderData = [] } = props?.orderData || {};
  const hasReported = useRef<Record<number, boolean>>({});

  const showLoading = useMemo(() => {
    return (
      <div className="user-order-skeleton">
        {/* <div className="user-order-title-skeleton skeletonLoading" /> */}
        <div className="user-order-content-skeleton">
          <div className="user-order-content-img-skeleton skeletonLoading" />
          <div className="user-order-contnet-right-skeleton">
            <div className="user-order-content-title-skeleton skeletonLoading" />
            <div className="user-order-content-desc-skeleton skeletonLoading" />
          </div>
        </div>
      </div>
    );
  }, []);

  return isFetching ? (
    showLoading
  ) : (
    <div className="user-order-main">
      <Slider
        className="user-order-list"
        {...DefaultSliderSettings}
        onInit={() => {
          if (orderData?.[0]?._type && !hasReported.current[0]) {
            goldLogMyTaoExp('order', `${spmKey[orderData?.[0]?._type]}_1`);
            hasReported.current[0] = true;
          }
        }}
        afterChange={(current: number) => {
          if (orderData?.[current]?._type && !hasReported.current[current]) {
            goldLogMyTaoExp(
              'order',
              `${spmKey[orderData?.[current]?._type]}_${current + 1}`,
            );
            hasReported.current[current] = true;
          }
        }}
      >
        {orderData.map((item, index) => {
          return (
            <div key={index} className="user-order" data-spm="order">
              {/* 待支付 */}
              {item._type === SLIDER_ITEM_TYPE.WaitPaymentInfo && (
                <div className="paying">
                  <div className="order-img">
                    <img
                      src={item.smartContent.itemImgUrl}
                      className="img"
                      alt=""
                      onError={(element: any) => {
                        element.target.src =
                          'https://gw.alicdn.com/imgextra/i2/O1CN01WAQeUe1KGKgvmze2c_!!6000000001136-2-tps-144-144.png';
                      }}
                    />
                  </div>
                  <div className="order-detail">
                    <div className="desc-title">
                      {item.smartContent.statusName}
                    </div>
                    <p className="desc-info">
                      <span className="payTime">
                        {item.smartContent.scheduledCloseTime}
                      </span>
                      <span className="invalid">失效</span>
                    </p>
                  </div>
                  <a
                    href={item.targetUrl}
                    target="_blank"
                    className="paymentBtn"
                    data-spm="dnonpayment"
                    onClick={(e) => {
                      e.stopPropagation();
                      if (item._type) {
                        goldLogMyTaoClk(
                          'order',
                          `${spmKey[item._type]}_${index + 1}`,
                        );
                      }
                    }}
                  >
                    支付
                  </a>
                </div>
              )}

              {/* 物流 */}
              {item._type === SLIDER_ITEM_TYPE.LogisticsDeliveryInfo && (
                <a
                  href={item.targetUrl}
                  target="_blank"
                  data-spm={`ddelivery_${index + 1}`}
                  className="receiving"
                  onClick={(e) => {
                    e.stopPropagation();
                    if (item._type) {
                      goldLogMyTaoClk(
                        'order',
                        `${spmKey[item._type]}_${index + 1}`,
                      );
                    }
                  }}
                >
                  <div className="order-img">
                    <img
                      src={item.smartContent.itemImgUrl}
                      alt=""
                      className="img"
                      onError={(element: any) => {
                        element.target.src =
                          'https://gw.alicdn.com/imgextra/i2/O1CN01WAQeUe1KGKgvmze2c_!!6000000001136-2-tps-144-144.png';
                      }}
                    />
                  </div>
                  <div className="order-detail">
                    <div className="desc-title">
                      {item.smartContent.statusName}
                    </div>
                    <p className="desc-info">{item.smartContent.subTitle}</p>
                  </div>
                  {
                    item.targetUrl ? <div
                      className="moreBtn"
                    >
                      查看详情
                    </div> : null
                  }
                </a>
              )}

              {/* 降价 */}
              {item._type === SLIDER_ITEM_TYPE.LowPrice && (
                <a
                  href={item.targetUrl}
                  target="_blank"
                  data-spm={`djiangjia_${index + 1}`}
                  className="lowPrice"
                  onClick={(e) => {
                    e.stopPropagation();
                    if (item._type) {
                      goldLogMyTaoClk(
                        'order',
                        `${spmKey[item._type]}_${index + 1}`,
                      );
                    }
                  }}
                >
                  <div className="order-img">
                    <img
                      src={item.smartContent.itemImgUrl}
                      alt=""
                      className="img"
                      onError={(e: any) => {
                        e.target.src =
                          'https://gw.alicdn.com/imgextra/i2/O1CN01WAQeUe1KGKgvmze2c_!!6000000001136-2-tps-144-144.png';
                      }}
                    />
                  </div>
                  <div className="order-detail">
                    <div className="desc-title">
                      {item.smartContent.statusName}
                    </div>
                    <p className="desc-info">{item.smartContent.subTitle}</p>
                  </div>
                </a>
              )}

              {/* 广告 */}
              {item._type === SLIDER_ITEM_TYPE.AdvInfo && (
                <a
                  href={item.targetUrl}
                  target="_blank"
                  data-spm={`dadv_${index + 1}`}
                  className={'receivingAD adv-img-wrapper'}
                  onClick={(e) => {
                    e.stopPropagation();
                    if (item._type) {
                      goldLogMyTaoClk(
                        'order',
                        `${spmKey[item._type]}_${index + 1}`,
                      );
                    }
                  }}
                >
                  <img
                    className="adv-img"
                    src={item.smartContent.itemImgUrl}
                    alt=""
                  />
                </a>
              )}
              {/* 兜底 */}
              {item._type === SLIDER_ITEM_TYPE.BackupInfo && (
                <a
                  href={item.targetUrl}
                  target="_blank"
                  data-spm={`dadv_${index + 1}`}
                  className={'receivingAD adv-img-wrapper'}
                  onClick={(e) => {
                    e.stopPropagation();
                    if (item._type) {
                      goldLogMyTaoClk(
                        'order',
                        `${spmKey[item._type]}_${index + 1}`,
                      );
                    }
                  }}
                >
                  <div className="order-img">
                    <div className="order-img-mask" />
                  </div>
                  <div className="order-detail" style={{ marginLeft: '13px' }}>
                    <div className="desc-title">
                      {item.smartContent.statusName}
                    </div>
                    <p className="desc-info">{item.smartContent.subTitle}</p>
                  </div>
                  {
                    item.targetUrl ? <div
                      className="moreBtn"
                    >
                      查看详情
                    </div> : null
                  }
                </a>
              )}
            </div>
          );
        })}
      </Slider>
    </div>
  );
}

export default memo(UserSlider);

interface IUserSlider {
  orderData: {
    isFetching: boolean;
    data: IOrderSliderItem[];
  };
}
