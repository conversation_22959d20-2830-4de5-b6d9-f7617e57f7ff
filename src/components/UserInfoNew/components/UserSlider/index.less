.user-order-main {
  position: relative;

  overflow: hidden;

  // width: 100%; //！！！
  // // height: 64px;
  // height: 40px;
  // // margin: 0 4px 12px 4px;
  // // margin: 4px 4px 12px 4px;
  // padding: 8px;

  // -webkit-border-radius: 8px;
  // -moz-border-radius: 8px;
  // border-radius: 8px;
  // background-color: var(--bg-color, #fff);

  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  box-sizing: border-box;
  padding: 12px 16px 16px 16px;
  width: 100%;
  height: 80px;
  margin-top: 0px;
}

.user-order-title {
  font-size: 12px;
  font-weight: 500;
  line-height: 16px;
  text-align: center;
  color: var(--primary-color, #1f1f1f);

  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;

  .user-order-title-arrow {
    font-size: 16px;
    line-height: 16px;
    transform: scale(0.5) rotateZ(-90deg);
  }
}

.user-order-list {
  overflow: hidden;
  width: 100%;
  height: 52px;

  .user-order {
    position: relative;
    overflow: hidden;
    width: 100%;
    height: 52px;
    .adv-img-wrapper{
      width: 100%;
      height: 52px;
      .adv-img {
        width: auto;
        height: 100%;
        margin: 0;
        cursor: pointer;
        object-fit: contain;
      }
    }

    & > a {
      border-radius: 12px;
      display: flex;
      flex-direction: row;
      flex-wrap: nowrap;
      align-items: center;
      width: calc(100% - 100px);
      height: 52px;
    }
    .img {
      width: 52px;
      height: 52px;
      margin-right: 13px;
    }
    .desc-title {
      display: flex;
      flex-direction: row;
      flex-wrap: nowrap;
      align-items: center;
      width: 100%;
      height: 22px;
      font-size: 14px;
      line-height: 22px;
      font-family: PingFang SC;
      font-weight: 500;
      color: var(--tbpc-primary-color, rgba(0, 0, 0, 0.92));
      margin-bottom: 2px;
    }
    .desc-info {
      font-family: SF Pro Display;
      font-size: 14px;
      line-height: 22px;
      display: inline-block;
      text-overflow: ellipsis;
      overflow: hidden;
      white-space: nowrap;
      width: 100%;
      color: var(--desc-color, rgba(0, 0, 0, 0.52));
    }

    .order-img {
      float: left;

      a {
        width: 100%;
        height: 100%;
      }

      img {
        width: 52px;
        height: 52px;
        border-radius: 8px;
      }
      .order-img-mask{
        background-image: var(--tbpc-my-tao-backup-icon, url('https://img.alicdn.com/imgextra/i3/O1CN01Gjggl11gfSGFcFlnH_!!6000000004169-2-tps-208-208.png'));
        background-size: contain;
        background-repeat: no-repeat;
        width: 52px;
        height: 52px;
        border-radius: 8px;
      }
    }

    .order-detail {
      display: flex;
      flex-direction: column;
      flex-wrap: nowrap;
      justify-content: center;
      width: calc(100% - 65px);
      height: 52px;
    }
  }

  .paying {
    display: block;
    height: 52px;

    p {
      letter-spacing: 0.25px;

      color: var(--tbpc-secondary-color, #7a7a7a);

      font-family: PingFangSC;
      font-size: 12px;
      font-weight: 400;
      line-height: 12px;

      .payTime {
        letter-spacing: 0.25px;
        color: var(--tb-brand-light-color, #ff5000);
        font-family: PingFangSC;
        font-size: 12px;
        font-weight: 400;
        line-height: 12px;
      }

      .invalid {
        letter-spacing: 0.25px;
        color: var(--secondary-color, #7a7a7a);
        font-family: PingFangSC;
        font-size: 12px;
        font-weight: 400;
        line-height: 12px;
      }
    }
  }
  .paymentBtn, .moreBtn {
    position: absolute;
    top: 8px;
    right: 2px;
    border-radius: 8px;
    border: 1px solid var(--tbpc-btn-border-color, rgba(128, 73, 0, 0.08));
    background: var(--tbpc-btn-bg2, rgba(255, 255, 255, 0.5));
    color: var(--tbpc-primary-color, rgba(0, 0, 0, 0.92));
    display: flex;
    flex-direction: row;
    flex-wrap: nowrap;
    box-sizing: border-box;
    padding: 5px 8px;
    justify-content: center;
    align-items: center;
    width: 80px;
    height: 32px;
    font-size: 14px;
    line-height: 22px;
    cursor: pointer;
    box-sizing: border-box;
  }
  .paymentBtn:hover, .moreBtn:hover{
    background: var(--tbpc-btn-bg2-hover, linear-gradient(0deg, rgba(0, 0, 0, 0.04), rgba(0, 0, 0, 0.04)), rgba(255, 255, 255, 0.5));
    border-color: var(--tbpc-btn-border-hover, rgba(0, 0, 0, 0.08));

  }
  .paymentBtn:active, .moreBtn:active{
    background: var(--tbpc-btn-bg2-active, linear-gradient(0deg, rgba(0, 0, 0, 0.08), rgba(0, 0, 0, 0.08)), rgba(255, 255, 255, 0.5));
    border-color: var(--tbpc-btn-border-color2-active, rgba(0, 0, 0, 0.08));
    
  }
  .payingNotShow,
  .receivingNotShow,
  .receivingADNotShow,
  .lowPriceNotShow {
    display: none;
  }
  .receivingAD,
  .receiving,
  .lowPrice {
    display: block;

    p {
      overflow: hidden;

      width: 156px;

      vertical-align: middle;
      white-space: nowrap;
      letter-spacing: 0.25px;
      text-overflow: ellipsis;

      color: var(--tbpc-secondary-color, #7a7a7a);

      font-family: PingFangSC;
      font-size: 12px;
      font-weight: 400;
      line-height: 16px;
    }
  }
}

.user-order-skeleton {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  box-sizing: border-box;
  padding: 12px 16px 16px 16px;
  width: 100%;
  height: 80px;
  overflow: hidden;

  @keyframes loadingAnimation {
    0%,
    100% {
      background: var(--tbpc-loading-bg1, rgba(0, 0, 0, 0.03));
    }
    50% {
      background: var(--tbpc-loading-bg2, rgba(0, 0, 0, 0.06));
    }
  }
  .skeletonLoading {
    animation: loadingAnimation 2s ease-in-out infinite;
  }

  .user-order-title-skeleton {
    width: 48px;
    height: 16px;

    border-radius: 4px;
    background: var(--tbpc-loading-bg1, rgba(0, 0, 0, 0.03));
  }

  .user-order-content-skeleton {
    display: flex;
    flex-direction: row;
    justify-content: flex-start;
    align-items: flex-start;
    width: 100%;
    height: 52px;
  }

  .user-order-content-img-skeleton {
    width: 52px;
    height: 52px;
    border-radius: 12px;
    background: var(--tbpc-loading-bg1, rgba(0, 0, 0, 0.03));
    margin-right: 8px;
  }

  .user-order-contnet-right-skeleton {
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    align-items: flex-start;
  }

  .user-order-content-title-skeleton {
    width: 200px;
    height: 20px;
    border-radius: 4px;
    background: rgba(0, 0, 0, 0.03);
    margin-top: 2px;
  }

  .user-order-content-desc-skeleton {
    width: 150px;
    height: 20px;
    border-radius: 4px;
    background: rgba(0, 0, 0, 0.03);
    margin-top: 4px;
  }
}
