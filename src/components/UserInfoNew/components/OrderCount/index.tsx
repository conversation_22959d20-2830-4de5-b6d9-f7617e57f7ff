import { AppearWeb } from '@ali/appear';
import type { IOrderCountRes } from '../../types';
import { goldLogMyTaoClk, goldLogMyTaoExp } from '../../utils';

function OrderCount(props: IOrderCount) {
  const { orderCount } = props;
  return (
    <AppearWeb
      onFirstAppear={() => {
        // goldLogMyTaoExp('profile', 'cart');
        goldLogMyTaoExp('profile', 'awaiting');
        goldLogMyTaoExp('profile', 'nonpayment');
        goldLogMyTaoExp('profile', 'delivery');
        goldLogMyTaoExp('profile', 'comment');
      }}
    >
      <div className="member-column" data-spm="profile">
        {/* <a
          data-spm="dcart"
          href="//cart.taobao.com"
          className="member-cart"
          onClick={() => {
            goldLogMyTaoClk('profile', 'cart');
          }}
        >
          <div className="stat-number">{orderCount.cart}</div><div className="stat-label">购物车</div>
        </a> */}
        <a
          data-spm="dawaiting"
          target="_blank"
          href="//buyertrade.taobao.com/trade/itemlist/list_bought_items.htm?action=itemlist/BoughtQueryAction&event_submit_do_query=1&tabCode=waitConfirm"
          className="member-awaiting"
          onClick={(e) => {
            e.stopPropagation();
            goldLogMyTaoClk('profile', 'awaiting');
          }}
        >
          <div className="stat-number">{orderCount.waitConfirm}</div><div className="stat-label">待收货</div>
        </a>
        <a
          data-spm="ddelivery"
          target="_blank"
          href="//buyertrade.taobao.com/trade/itemlist/list_bought_items.htm?action=itemlist/BoughtQueryAction&event_submit_do_query=1&tabCode=waitSend"
          className="member-delivery"
          onClick={(e) => {
            e.stopPropagation();
            goldLogMyTaoClk('profile', 'delivery');
          }}
        >
          <div className="stat-number">{orderCount.waitSend}</div><div className="stat-label">待发货</div>
        </a>
        <a
          data-spm="dnonpayment"
          target="_blank"
          href="//buyertrade.taobao.com/trade/itemlist/list_bought_items.htm?action=itemlist/BoughtQueryAction&event_submit_do_query=1&tabCode=waitPay"
          className="member-nonpayment"
          onClick={(e) => {
            e.stopPropagation();
            goldLogMyTaoClk('profile', 'nonpayment');
          }}
        >
          <div className="stat-number">{orderCount.waitPay}</div><div className="stat-label">待付款</div>
        </a>
        <a
          data-spm="dcomment"
          target="_blank"
          href="//buyertrade.taobao.com/trade/itemlist/list_bought_items.htm?action=itemlist/BoughtQueryAction&event_submit_do_query=1&tabCode=waitRate"
          className="member-comment"
          onClick={(e) => {
            e.stopPropagation();
            goldLogMyTaoClk('profile', 'comment');
          }}
        >
          <div className="stat-number">{orderCount.waitRate}</div><div className="stat-label">待评价</div>
        </a>
      </div>
    </AppearWeb>
  );
}

export default OrderCount;

interface IOrderCount {
  orderCount: IOrderCountRes;
}
