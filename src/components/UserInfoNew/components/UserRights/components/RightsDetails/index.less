.rights-details {
  overflow: hidden;
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  box-sizing: border-box;
  padding: 12px 16px 16px 16px;
  width: 100%;
  height: 80px;

  .rights-slick-list {
    width: 100%;
    height: 100%;
  }

  .rights-details-item {
    display: flex !important;
    align-items: center;
    width: 100%;
    height: 100%;
    position: relative;

    .rights-details-item-left-container {
      display: flex;
      flex-direction: row;
      flex-wrap: nowrap;
      align-items: center;
      width: calc(100% - 100px);
      height: 100%;
    }

    .rights-details-item-pic-container {
      width: 52px;
      height: 52px;
      border-radius: 8px;
      display: flex;
      align-items: center;
      justify-content: center;
      overflow: hidden;
      background-color: var(--tbpc-img-bg, rgba(255, 255, 255, 0.48));

      .rights-details-item-pic {
        width: 100%;
        height: 100%;
        border-radius: 8px;
        object-fit: contain;
      }

      // .rights-details-item-pic-mask{
      //   position: absolute;
      //   top: 0;
      //   left: 0;
      //   width: 52px;
      //   height: 52px;
      //   background-color: var(--tbpc-img-bg, rgba(255, 255, 255, 0.48));
      //   border-radius: 8px;
      //   z-index: 2;
      // }
    }

    .rights-details-item-center {
      display: flex;
      flex-direction: column;
      flex-wrap: nowrap;
      justify-content: center;
      align-items: flex-start;
      width: calc(100% - 80px);
      height: 48px;
      margin-left: 12px;

      .rights-details-item-title, .rights-details-item-desc {
        font-size: 14px;
        font-weight: 500;
        line-height: 22px;
        overflow: hidden;
        max-width: 160px;
        white-space: nowrap;
        text-overflow: ellipsis;
        word-break: break-all;
        color: var(--tbpc-client-hover-color, #ff5000);

        .rights-details-item-title-text {
          color: var(--tbpc-primary-color, rgba(0, 0, 0, 0.92));
        }
      }
      .rights-details-item-desc {
        font-weight: normal;
      }

      .rights-details-item-desc {
        font-size: 12px;
        font-weight: 400;
        line-height: 16px;
        color: var(--tbpc-client-hover-color, #ff5000);
        margin-top: 4px;
      }
    }

    .rights-details-item-right {
      position: absolute;
      right: 2px;
      top: 50%;
      transform: translateY(-50%);

      .rights-details-item-right-arrow {
        font-size: 24px;
        line-height: 24px;
        color: var(--tbpc-client-hover-color, #ff5000);
        transform: scale(0.5) rotateZ(-90deg);
      }

      .rights-details-item-btn {
        border-radius: 8px;
        border: 1px solid var(--tbpc-btn-border-color, rgba(128, 73, 0, 0.08));
        background: var(--tbpc-btn-bg, #FFFCFA);
        color: var(--tbpc-primary-color, rgba(0, 0, 0, 0.92));
        display: flex;
        flex-direction: row;
        flex-wrap: nowrap;
        box-sizing: border-box;
        padding: 5px 8px;
        justify-content: center;
        align-items: center;
        width: 80px;
        height: 32px;
        font-size: 14px;
        line-height: 22px;
        cursor: pointer;
        box-sizing: border-box;
      }

      .rights-details-item-btn:hover {
        background: var(--tbpc-btn-bg-hover, linear-gradient(0deg, rgba(0, 0, 0, 0.04), rgba(0, 0, 0, 0.04)), #FFFCFA);
        border: 1px solid var(--tbpc-btn-border-color-hover, rgba(128, 58, 26, 0.08));
      }

      .rights-details-item-btn:active {
        background: var(--tbpc-btn-bg-active, linear-gradient(0deg, rgba(0, 0, 0, 0.04), rgba(0, 0, 0, 0.04)), #FFFCFA);
        border: 1px solid var(--tbpc-btn-border-color-active, rgba(128, 58, 26, 0.08));

      }
      .rights-details-item-btn-vip{
        background: var(--tbpc-btn-bg-vip,#FEF5EA);
        border: 1px solid var(--tbpc-btn-border-color-vip, rgba(128, 73, 0, 0.08));
      }

      .rights-details-item-btn-vip:hover {
        background: var(--tbpc-btn-bg-hover-vip, linear-gradient(0deg, rgba(0, 0, 0, 0.04), rgba(0, 0, 0, 0.04)), #FEF5EA);
        border: 1px solid var(--tbpc-btn-border-color-hover-vip, rgba(128, 73, 0, 0.08));
      }

      .rights-details-item-btn-vip:active {
        background: var(--tbpc-btn-bg-active-vip, linear-gradient(0deg, rgba(0, 0, 0, 0.08), rgba(0, 0, 0, 0.08)), #FEF5EA);
        border: 1px solid var(--tbpc-btn-border-color-active-vip, rgba(128, 73, 0, 0.08));
      }
    }
  }

  .rights-details-item-skeleton {
    width: calc(100% - 16px);
    height: 40px;
    display: flex;
    align-items: center;
    margin-left: 8px;
    position: relative;

    @keyframes loadingAnimation {

      0%,
      100% {
        background: rgba(0, 0, 0, 0.03);
      }

      50% {
        background: rgba(0, 0, 0, 0.06);
      }
    }

    .skeletonLoading {
      animation: loadingAnimation 2s ease-in-out infinite;
    }

    .rights-details-item-pic-container-skeleton {
      width: 40px;
      height: 40px;
      background: rgba(0, 0, 0, 0.03);
      border-radius: 4px;
    }

    .rights-details-item-center-skeleton {
      margin-left: 8px;

      .rights-details-item-title-skeleton {
        width: 72px;
        height: 12px;
        border-radius: 4px;
        background: rgba(0, 0, 0, 0.03);
      }

      .rights-details-item-desc-skeleton {
        width: 56px;
        height: 12px;
        border-radius: 4px;
        background: rgba(0, 0, 0, 0.03);
        margin-top: 6px;
      }
    }

    .rights-details-item-right-skeleton {
      width: 40px;
      height: 24px;
      border-radius: 4px;
      background: rgba(0, 0, 0, 0.03);
      position: absolute;
      right: 0;
    }
  }
}