import { queryMultiResources } from '../../utils/ald-request';
import { isTrue } from '../../utils';
import { mtopRequest } from '../../utils/mtop';
import type {
  IQueryBagRes,
  IQueryBenefitRemindRes,
  IQueryBenefitRes,
  IQueryCouponRemindRes,
  IQueryOrderListCountRes,
  IQueryOrderStatusRes,
  IQueryTaoCoinRes,
} from './types';
import { formatArrToObj } from './utils';

// slider中待付款/物流信息查询
export const queryOrderStatus = () => {
  return mtopRequest({
    // https://yuque.antfin-inc.com/docs/share/4fbffdd7-44f3-4bf0-bfba-5001335bde22?#
    api: 'mtop.taobao.wireless.home.awesome.pc.get',
    v: '1.0',
    data: {
      containerParams: JSON.stringify({
        entrance_home_main_pc: {
          deltaSections: [],
          passParams: { lastVersion: 'v1' },
          bizParams: {},
          baseCacheTime: '0',
          deltaCacheTime: '0',
          requestType: 'pullRefresh',
        },
      }),
      type: 'GET',
      timeout: 10000,
    },
  }) as Promise<IQueryOrderStatusRes>;
};

// 购物车
export const queryBag = () => {
  return mtopRequest({
    api: 'mtop.trade.queryBagCount',
    v: '1.0',
    data: {
      cartFrom: 'main_site',
      extStatus: 0,
      netType: 0,
    },
    ecode: 0,
    type: 'GET',
    timeout: 5000,
  }) as Promise<IQueryBagRes>;
};

// 待发货/待收货/待付款/待评价
export const queryOrderListCount = () => {
  return mtopRequest({
    api: 'mtop.order.taobao.countv2',
    v: '1.0',
    data: {
      tabCodes: 'waitConfirm,waitSend,waitPay,waitRate',
    },
    ecode: 0,
    type: 'GET',
    dataType: 'jsonp',
    timeout: 5000,
  }) as Promise<IQueryOrderListCountRes>;
};

// 催领催用(红包)
export const queryBenefitRemind = () => {
  return mtopRequest({
    api: 'mtop.alibaba.fc.api.maoxland.ContainerFacade.singleView',
    v: '1.0',
    data: {
      projectName: 'PcTaobao',
      responseCode: 'PcTaobaoBenefitRemind',
      params: JSON.stringify({
        bizType: 'custom',
        bizId: 3,
      }),
    },
  }) as Promise<IQueryBenefitRemindRes>;
};

// 催领催用（优惠券）
export const queryCouponRemind = () => {
  return mtopRequest({
    api: 'mtop.alibaba.fc.api.maoxland.ContainerFacade.singleView',
    v: '1.0',
    data: {
      projectName: 'PcTaobao',
      responseCode: 'PcTaobaoBenefitRemind',
      params: JSON.stringify({
        bizType: 'custom',
        bizId: 4,
      }),
    },
  }) as Promise<IQueryCouponRemindRes>;
};

// 置顶催领催用 红包/优惠券
export const queryTopRemind = () => {
  return mtopRequest({
    api: 'mtop.alibaba.fc.api.maoxland.ContainerFacade.singleView',
    v: '1.0',
    data: {
      projectName: 'PcTaobao',
      responseCode: 'PcTaobaoBenefitRemind',
      params: JSON.stringify({
        bizType: 'custom',
        bizId: 6,
      }),
    },
  }) as Promise<IQueryCouponRemindRes>;
};

// 我的红包和优惠券统计信息
export const queryBenefitCount = () => {
  return mtopRequest({
    api: 'mtop.wallet.benefit.statistic',
    v: '1.0',
    data: {
      scene: 'shoutao',
      targetList: JSON.stringify([
        'redEnvelope',
        'coupon',
      ]),
      extraData: '{}',
    },
    ecode: 1,
    needLogin: true,
    timeout: 5000,
    // 注意此处不是我们常规的ttid标准结构，这里是特殊的ttid，仅卡券包服务端消费，消费规则：取到ttid后拿@之后_之前的那个字段，与拉菲配置后台的code对应，拉菲后台就是「pc」
    ttid: '1@pc_mac_1.0.0#pc',
  }).then((res: { success: any; model: { statList: Array<{ mainType: 'redEnvelope' | 'coupon'; statisticCnt: string }> } }) => {
    if (res?.success && res?.model?.statList?.length > 0) {
      const data = formatArrToObj(res.model.statList, 'mainType', 'statisticCnt');
      return {
        data,
        success: true,
      };
    } else {
      return {
        data: {},
        success: false,
      };
    }
  }).catch(() => {
    return {
      data: {},
      success: false,
    };
  }) as Promise<IQueryBenefitRes>;
};

// 我的淘金币总数
export const queryTaoCoinCount = () => {
  return mtopRequest({
    api: 'mtop.taobao.pc.growth.taocoin.queryUserTaoCoin',
    v: '1.0',
    data: {},
    ecode: 1,
    needLogin: true,
    timeout: 5000,
  }).then((res: { code: number; data: { coinAmount: number; coinSaving: number } }) => {
    if (res?.code === 200 && res?.data) {
      return {
        data: res.data,
        success: true,
      };
    } else {
      return {
        data: {},
        success: false,
      };
    }
  }).catch(() => {
    return {
      data: {},
      success: false,
    };
  }) as Promise<IQueryTaoCoinRes>;
};

/**
 * 查询红包的去使用链接
 * 取数消费示意：
 *  data[benefitItem?.[红包的唯一标识]]?.jumpUrl;
 *  其中benefitItem就是官亭下发的红包数据对象
 * 如果任意红包的跳转链接无法匹配到（此时表示运营没做人工干预），请使用卡券包链接作为兜底：https://i.taobao.com/my_itaobao/coupon?defaultTab=redEnvelope
 */
export const queryRedEnvelopeUseUrl = () => {
  const RED_ENVELOPE_RES_ID = 35633211;
  return queryMultiResources<Record<number, any>>({
    resIdList: [RED_ENVELOPE_RES_ID],
  }).then(res => {
    if (isTrue(res?.success) && isTrue(res?.data?.[RED_ENVELOPE_RES_ID]?.success)) {
      const config = formatArrToObj(res?.data?.[RED_ENVELOPE_RES_ID]?.data, 'ruleId');
      if (config) {
        return {
          data: config,
          success: true,
        };
      } else {
        return {
          data: {},
          success: false,
        };
      }
    } else {
      return {
        data: {},
        success: false,
      };
    }
  }).catch(() => {
    return {
      data: {},
      success: false,
    };
  }) as Promise<{ data: { [x: string]: { jumpUrl: string } }; success: boolean }>;
};


