// 登录态
export enum LOGIN_STATUS {
  STRONG = 2,
  WEAK = 1,
  NONE = 0,
}

// 购物车/待收货/待发货/待付款/待评价
export interface IOrderCountRes {
  waitConfirm: number;
  waitPay: number;
  waitRate: number;
  waitSend: number;
  cart: number;
}

export interface IQueryBagRes {
  count?: number;
  [key: string]: any;
}

export interface IOrderStatusItem {
  tabCode: TAB_CODE_KEY;
  count: number;
}

export interface IQueryOrderListCountRes {
  result: IOrderStatusItem[];
}

export enum TAB_CODE_KEY {
  'waitConfirm' = 'waitConfirm',
  'waitPay' = 'waitPay',
  'waitRate' = 'waitRate',
  'waitSend' = 'waitSend',
}

export interface ISmartContent {
  scheduledCloseTime?: string;
  statusName?: string;
  itemImgUrl?: string;
  subTitle?: string;
}

export enum SLIDER_ITEM_TYPE {
  WaitPaymentInfo = 'waitPaymentInfo',
  LogisticsDeliveryInfo = 'logisticsDeliveryInfo',
  LowPrice = 'lowPrice',
  AdvInfo = 'advInfo',
  BackupInfo = 'backupInfo',
}

export interface IOrderSliderItem {
  smartContent: ISmartContent;
  _type?: SLIDER_ITEM_TYPE;
  targetUrl: string;
  content?: Record<string, any>;
  eventParam?: Record<string, any>;
  exContent?: Record<string, any>;
  ext?: Record<string, any>;
  [key: string]: any;
}

export interface ISubSection {
  waitPaymentInfo?: {
    [key: string]: any;
  };
  logisticsDeliveryInfo?: {
    [key: string]: any;
  };
}
// 轮播区域
export interface IQueryOrderStatusRes {
  containers: {
    entrance_home_main_pc: {
      base: {
        sections: {
          sectionBizCode: string;
          subSection: ISubSection;
        }[];
      };
    };
  };
}

// 降价提醒
export interface ILowPriceItem {
  title: string;
  url: string;
  __pos__: number;
  __track__: string;
}

export interface IQueryLowPriceData {
  msgCode: string;
  msgInfo: string;
  success: boolean;
  data: ILowPriceItem[];
  cost: number;
  currentPage: number;
  pageSize: number;
  totalPage: number;
  trackParams: Record<string, any>;
  bottom: boolean;
  resId: string;
  featureValue: number;
  subTitle: string;
  cartUrl: string;
  title: string;
  needBottom: boolean;
}

export interface IQueryLowPriceRes {
  success: boolean;
  data: {
    [key: string]: IQueryLowPriceData;
  };
  bottom: boolean;
}

export interface ILoginSliderItem {
  itemImgUrl: string;
  targetUrl: string;
}
// 登录广告
export interface ILoginSliderRes {
  success: boolean;
  data: {
    [key: string]: ILoginSliderItem;
  };
  bottom: boolean;
}

// 未登录营销海报
export interface INoLoginAd {
  canShow: boolean;
  bgImage?: string;
  ruleTitle?: string;
  ruleUrl?: string;
}

// 登录营销海报
export interface IUserInfo {
  nick: string;
  avatar: string;
  helloText: string;
}

export interface IAvatarConfig {
  bgColor?: string;
  iconUrl?: string;
}

// 营销中心数据
// 功能区
export interface IFunctionalArea {
  distinctId: string;
  dataSetId: number;
  currentAldResId: string;
  link: string;
  icon: string;
  text: string;
  id: string;
  __pos__: number;
  __track__: string;
  [key: string]: any;
}
// 外部入口
export interface IExternalEntrance {
  distinctId: string;
  dataSetId: number;
  currentAldResId: string;
  link: string;
  icon: string;
  text: string;
  id: string;
  __pos__: number;
  __track__: string;
  [key: string]: any;
}
// 未登录营销海报
export interface INoLoginEntrance {
  ruleTitle: string;
  distinctId: string;
  dataSetId: number;
  currentAldResId: string;
  bgImage: string;
  id: string;
  ruleUrl: string;
  __pos__: number;
  __track__: string;
  [key: string]: any;
}
// 最下面滚动的slider
export interface IMarketingSlider {
  bigImg: string;
  distinctId: string;
  smallImg: string;
  dataSetId: number;
  currentAldResId: string;
  link: string;
  id: string;
  __pos__: number;
  __track__: string;
  [key: string]: any;
}

export enum SLIDER_SIZE {
  SMALL = 'small',
  LARGE = 'large',
}

// 个人中心权益兜底
export interface IBenefitBottomSlider {
  distinctId?: string;
  dataSetId?: number;
  currentAldResId?: string;
  id?: string;
  targetUrl: string;
  // itemImgUrl: string;
  bottomText: string;
  __pos__?: number;
  __track__?: string;
  type?: string;
  btnText?: string;
  subText?: string;
}

// 个人中心催领催用
export interface IBenefitRemoteSlider {
  amountThreshold: number;
  currentTimestamp: number;
  displayAmountThreshold: string;
  displayTotalFee: string;
  fundId: number;
  gmtInvalid: string;
  invalidTimestamp: number;
  subBizType: number;
  title: string;
  totalFee: number;
  // 资产类型：(服务端下发) coupon-优惠券 fund-红包
  assetType?: string;
  // 资产类型：（前端使用）coupon-优惠券 fund-红包
  type?: string;
  subAssetType?: string;
  // coupon跳转时的携带参数
  extraData: {
    couponTag: string;
    templateCode: string;
  };
  targetUrl: string;
  jumpUrl?: string;
}

export interface IBusinessEntry {
  functionalArea?: IFunctionalArea[];
  marketingEntrance?: INoLoginEntrance[];
  externalEntrance?: IExternalEntrance[];
  marketingSlider?: IMarketingSlider[];
  benefitSlider?: (IBenefitBottomSlider | IBenefitRemoteSlider)[];
}

export interface ILoginAreaConfig {
  topColor?: string;
  bottomColor?: string;
  loginTitle?: string;
  loginSubtitle?: string;
}

export interface IQueryBenefitRemindRes {
  value: {
    benefits: HongBaoData[];
  };
}

export interface IQueryCouponRemindRes {
  value: {
    benefits: CouponData[];
  };
}

export interface IQueryBenefitRes {
  success: boolean;
  data: {
    // 红包
    redEnvelope: number;
    // 券
    coupon: number;
  };
}

export interface IQueryTaoCoinRes {
  success: boolean;
  data: {
    // 淘金币数量
    coinAmount: number;
    // 淘金币可节省金额
    coinSaving: number;
  };
}

// 红包/优惠券催领催用结构
export interface HongBaoData {
  amountThreshold: number;
  currentTimestamp: number;
  displayAmountThreshold: string;
  displayTotalFee: string;
  fundId: number;
  gmtInvalid: string;
  invalidTimestamp: number;
  subBizType: number;
  title: string;
  totalFee: number;
  targetUrl: string;
  type: string;
  // 资产类型：coupon-优惠券
  assetType: string;
  [key: string]: any;
}

export interface CouponData extends HongBaoData {
  extraData: {
    couponTag: string | number;
    templateCode: string | number;
  };
}

export interface LinkData {
  distinctId: string;
  dataSetId: number;
  currentAldResId: string;
  id: string;
  targetUrl: string;
  // itemImgUrl: string;
  bottomText: string;
  btnText?: string;
  subText?: string;
}

export interface RewardStatus {
  success: boolean;
  num: number | string; // num可以是字符串或数字
}

export interface RewardData {
  type: string;
  success: boolean;
  num: string | number;
  name: string;
  url: string;
  logkey: string;
}

export interface IBenefitBaseCount {
  redEnvelop: RewardStatus;
  coupon: RewardStatus;
  taoCoin: RewardStatus;
}

export interface IBenefitCount extends IBenefitBaseCount {
  isFetching: boolean;
}

type IRightsDetailsData = (HongBaoData | LinkData)[];
export interface IUserRightsProps {
  rightsDetailsData?: IRightsDetailsData;
  benefitSliderList: (IBenefitBottomSlider | IBenefitRemoteSlider)[];
  benefitCount: IBenefitCount;
  showAvatarConfig: boolean;
}
