import { goldlogRecord } from '../../../utils/goldlog';
import { getPageSpm } from '../../../utils/spm';
import { reportCustom } from '../../../utils/jstracker';
import type { IEvoContextValue } from '../../../contexts/EvoContext';

export const getHello = () => {
  // 判断早上、上午、中午、下午和晚上
  const now = new Date();
  const hour = now.getHours();
  let timeText = '您好！';
  if (hour >= 5 && hour < 8) {
    timeText = '早上好'; // 5:00 - 7:59
  } else if (hour >= 8 && hour < 12) {
    timeText = '上午好'; // 8:00 - 11:59
  } else if (hour >= 12 && hour < 13) {
    timeText = '中午好'; // 12:00 - 12:59
  } else if (hour >= 13 && hour < 18) {
    timeText = '下午好'; // 13:00 - 17:59
  } else {
    timeText = '晚上好'; // 18:00 - 4:59
  }
  return timeText;
};

export const reportLog = (msg: string) => {
  reportCustom({
    code: 'user-center-js-error',
    message: msg,
    sampling: 1,
  });
};

export const goldLogYouHuiClk = (spmC: string, spmD: string) => {
  goldlogRecord({
    logKey: '/tbpcclient.newpc.youhui',
    gmKey: 'CLK',
    goKey: {
      spm: getPageSpm(spmC, spmD),
    },
  });
};

export const goldLogYouHuiExp = (spmC: string, spmD: string) => {
  goldlogRecord({
    logKey: '/tbpcclient.newpc.youhui',
    gmKey: 'EXP',
    goKey: {
      spm: getPageSpm(spmC, spmD),
    },
  });
};

export const goldLogUserClk = (spmC: string, spmD: string) => {
  goldlogRecord({
    logKey: '/tbpcclient.newpc.myprofile',
    gmKey: 'CLK',
    goKey: {
      spm: getPageSpm(spmC, spmD),
    },
  });
};

export const goldLogUserExp = (spmC: string, spmD: string) => {
  goldlogRecord({
    logKey: '/tbpcclient.newpc.myprofile',
    gmKey: 'EXP',
    goKey: {
      spm: getPageSpm(spmC, spmD),
    },
  });
};

export const goldLogMyTaoClk = (spmC: string, spmD: string) => {
  goldlogRecord({
    logKey: '/tbpcclient.newpc.dingdan',
    gmKey: 'CLK',
    goKey: {
      spm: getPageSpm(spmC, spmD),
    },
  });
};

export const goldLogMyTaoExp = (spmC: string, spmD: string) => {
  goldlogRecord({
    logKey: '/tbpcclient.newpc.dingdan',
    gmKey: 'EXP',
    goKey: {
      spm: getPageSpm(spmC, spmD),
    },
  });
};

export const formatArrToObj = (arr: any[], key: string, valueKey?: string) => {
  if (!arr?.length) {
    return null;
  }
  return arr.reduce((acc, curr) => {
    const id = curr[key];
    if (id) {
      acc[id] = valueKey ? curr[valueKey] : curr;
    }
    return acc;
  }, {});
};

export const isCartCountValid = (cartNo: any) => {
  return typeof cartNo === 'number' && cartNo > 0;
};
