import AdaptiveGrid, { MediumBlock } from '../components/AdaptiveGrid';
import RecommendBanner from '../components/RecommendBanner';
import styles from './home.module.css';
import TbhomeClientOrderLogistics from '@ali/tbhome-client-order-logistics';
import TbhomeClientShoppingCart from '@ali/tbhome-client-shopping-cart';
import Search from '@/components/Search';
import BusinessEntry from '@/components/BusinessEntry';
import { coreAldMultiRequestConfig } from '@/coreAldConfig';
import { CoreAldDataProvider } from '@/contexts/CoreAldDataContext';
import { isTrue, setTBPCClientLayout, isLoginFromLibAsync, HomeUrlHref } from '@/utils';
import { isLogin } from '@/utils/cookie';
import { useEffect, useMemo, useState } from 'react';
import { logPerf } from '@/utils/jstracker';
import { loadWebPushIframe } from '@/utils/webpush';
import { goldLogMyTaoClk, goldLogMyTaoExp } from '@/components/UserInfoNew/utils';
// import { queryOrderListCount, queryOrderStatus } from '@/components/UserInfoNew/services';
import Feedback from '@/components/Feedback';
import { getPageSpm } from '@/utils/spm';
import { goldLogShoppingCartClk, goldLogShoppingCartExp, goldLogShoppingCartProductClk } from '@/utils/goldlog';
import { LOGIN_STATUS } from '@/components/UserInfoNew/types';
// import type { IRecommendFeedProps } from '@ali/pc-tb-guess-u-link-feeds';
import GuessULikeInfo from '@ali/pc-tb-guess-u-link-feeds';


setTBPCClientLayout();
loadWebPushIframe();
const fullHomeUrl = `${HomeUrlHref}?spm=${getPageSpm('mytao', 'd_bg')}`;

export default function Home() {
  // 是否登录
  const [login, setLogin] = useState<LOGIN_STATUS>(LOGIN_STATUS.NONE);
  useMemo(() => {
    if (isTrue(isLogin())) {
      setLogin(LOGIN_STATUS.WEAK);
    }
    if (typeof window?.isLoginFromLibPromise?.then === 'function') {
      window.isLoginFromLibPromise.then((res) => {
        if (res && res?.data) {
          setLogin(LOGIN_STATUS.STRONG);
        } else {
          setLogin(LOGIN_STATUS.NONE);
        }
      });
    }
  }, []);

  useEffect(() => {
    let lastLoginState = false;
    isLoginFromLibAsync().then(loginState => {
      lastLoginState = loginState;
    });

    const handleVisibilityChange = async () => {
      if (document.visibilityState === 'visible') {
        const currentLoginState = await isLoginFromLibAsync();
        // 如果之前未登录，现在登录了，则刷新页面
        if (!lastLoginState && currentLoginState) {
          window.location.reload();
        }
        lastLoginState = currentLoginState;
      }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);
    // 统计perf
    const time = typeof performance !== 'undefined' && typeof performance?.now === 'function' ? performance.now() : 0;
    logPerf('pageIndexRenderEnd', time);
    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
    };
  }, []);

  return (
    <CoreAldDataProvider aldReqConfig={coreAldMultiRequestConfig}>
      <div className={styles.app}>
        <div className={styles.header} />
        <div className={styles.logoSection}>
          <div className={styles.logoWrapper}>
            <div
              className={styles.logo}
              role="img"
              aria-label="淘宝客户端Logo"
            />
          </div>
          <div className={styles.searchContainer}>
            <Search
              {...{
                referer: 'pc_taobao',
                fromSource: 'tbClient',
                tabData: [
                  {
                    value: 'item',
                    label: '宝贝',
                    selected: true,
                    action: '//s.taobao.com/search?tab=all',
                  },
                ],
                renderGuessWant: ['item'],
                imgSearchButtonShowOutside: true,
                allowPreLoad: false,
              }}
            />
          </div>
        </div>

        <div className={styles.middleHeader} />
        <BusinessEntry />
        <AdaptiveGrid>
          {
            login === LOGIN_STATUS.WEAK && <>
              <MediumBlock />
              <MediumBlock />
            </>
          }
          {
            login === LOGIN_STATUS.STRONG && <MediumBlock>
              <TbhomeClientOrderLogistics
                goldLogMyTaoClk={goldLogMyTaoClk}
                goldLogMyTaoExp={goldLogMyTaoExp}
                // queryOrderListCount={queryOrderListCount}
                // queryOrderStatus={queryOrderStatus}
                homeUrl={fullHomeUrl}
              />
            </MediumBlock>
          }
          {
            login === LOGIN_STATUS.STRONG && <MediumBlock>
              <TbhomeClientShoppingCart
                goldLogShoppingCartClk={goldLogShoppingCartClk}
                goldLogShoppingCartExp={goldLogShoppingCartExp}
                goldLogShoppingCartProductClk={goldLogShoppingCartProductClk}
              />
            </MediumBlock>
          }
        </AdaptiveGrid>
        <div className={styles.recommendBannerContainer}>
          <RecommendBanner />
        </div>
        <Feedback />
        <div className={styles.gapWithGuessULike} />
        <GuessULikeInfo
          scene="tghome-client"
          tabConfig={{}}
          fetchConfig={{
            tppId: '30986',
            secondTabResId: '34215459',
            dislikePoolId: '36812159',
            needABRequest: true,
          }}
          uiConfig={{
            showScrollGuide: true,
            showBuyCount: true,
            showDisLikeBtn: true,
            showNdGuide: false,
            scrollTopValue: 1,
            columnRule: (screens: string[]) => {
              if (screens.includes('xxl') || screens.includes('xl')) {
                return 6;
              } else {
                return 5;
              }
            },
          }}
          spmConfig={{
            logKey: '/tbindex.newpc.guessitem',
            spmC: 'client',
          }}
        />
      </div>
    </CoreAldDataProvider>
  );
}

