/// <reference types="@ice/app/types" />
import type { IAldReqConfig, IAldReqParsedRes } from './utils/ald-request';
declare global {
  interface Window {
    SearchSuggest: any;
    lib: any;
    isLoginFromLibPromise: Promise<any>;
    __pc_index_head_prefetch_map_non_ald__?: Record<string, Promise<any>>;
    __pc_index_head_prefetch_list__?: any[];
    $coreAldMultiRequestConfig: Record<string, IAldReqConfig>;
    $parseAldMultiRequestConfig: (
      config: Record<string, IAldReqConfig>
    ) => IAldReqParsedRes;
    JSTracker2: any;
    _perfInfo: any;
    $reportPerfLog: (key: string, value: number) => void;
    $system: any;
  }
}
