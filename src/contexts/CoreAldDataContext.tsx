import type { PropsWithChildren } from 'react';
import { createContext, useEffect, useMemo, useState } from 'react';
import type {
  IAldDataParam,
  IAldReqConfig,
  IAldReqParsedRes } from '../utils/ald-request';
import {
  getAldMultiRequestParams,
  queryMultiResourcesBase,
} from '../utils/ald-request';
import { commonBackup, IBackupDataFrom } from '../utils/backup';
import { isTrue } from '../utils/index';
import type {
  ICacheData } from '../utils/cache';
import {
  defaultStoreName,
  generateUniqueLSKey,
  getCacheData,
  setCacheData,
} from '../utils/cache';
import { allSettled } from '../utils/promise-all-settled';
import { getLocalStorageItem } from '../utils/storage';
import { logPerf, reportCustom, reportPerfCustom } from '../utils/jstracker';
import { $staticConfig } from '@/utils/constant';

interface IProviderParams {
  children: PropsWithChildren<any>;
  aldReqConfig: Record<string, IAldReqConfig>;
}

export enum RequestStatus {
  ERROR = -1,
  LOADING = 0,
  SUCCESS = 1,
}
interface IErrorData {
  message?: string;
  originData: any;
}

export enum IDataFrom {
  Ald = 'ald',
  Default = '',
}

export interface IResponse {
  from: IDataFrom | IBackupDataFrom;
  status: RequestStatus;
  error: IErrorData | null;
  data: any;
  originResponse: any;
}

const cacheAbility = $staticConfig?.cacheAbility || {};
const useCacheFirst = isTrue(cacheAbility?.useCacheFirst);
const useLSForCacheFirst = isTrue(cacheAbility?.useLSForCacheFirst);

// 处理ald配置
let aldMultiRequestConfig: IAldReqParsedRes;
const parseAldRequestConfig = () => {
  try {
    if (!aldMultiRequestConfig) {
      aldMultiRequestConfig = window?.$parseAldMultiRequestConfig?.(window?.$coreAldMultiRequestConfig);
    }
  } catch (e) { }
};
parseAldRequestConfig();

// 确定缓存优先的资源位
const getCacheFirstKeyList = () => {
  try {
    const { keyList } = aldMultiRequestConfig || {};
    const useCacheFirstKeyList = keyList.filter((key: string) => {
      if (isTrue(window?.$coreAldMultiRequestConfig?.[key]?.useCacheFirst)) {
        return true;
      }
      return false;
    });
    return useCacheFirstKeyList;
  } catch (e) {
    return [];
  }
};

// 缓存优先：使用localStorage缓存
const preLSCacheProcess = (): any => {
  try {
    if (!useCacheFirst) {
      return null;
    }
    const useCacheFirstKeyList = getCacheFirstKeyList();
    if (!useCacheFirstKeyList.length) {
      return null;
    }
    if (useLSForCacheFirst) {
      const { keyToResIdMap } = aldMultiRequestConfig;
      const cacheData = useCacheFirstKeyList.map((key: string) => {
        const resId = keyToResIdMap[key];
        const uniqueKey = generateUniqueLSKey(resId, defaultStoreName);
        const data = getLocalStorageItem(uniqueKey);
        return {
          status: 'fulfilled',
          value: {
            success: !!data,
            data,
          },
        };
      });
      return cacheData;
    }
    return null;
  } catch (e) {
    return null;
  }
};

// 缓存优先：使用indexDB缓存
const preIndexDBCacheProcess = async (): Promise<any> => {
  try {
    if (!useCacheFirst) {
      return null;
    }
    const useCacheFirstKeyList = getCacheFirstKeyList();
    if (!useCacheFirstKeyList.length) {
      return null;
    }
    if (useLSForCacheFirst) {
      return null;
    }
    const { keyToResIdMap } = aldMultiRequestConfig;
    const cachedPromise = useCacheFirstKeyList.map((key: string) => {
      const resId = keyToResIdMap[key];
      return getCacheData(resId);
    });
    return allSettled(cachedPromise);
  } catch (e) {
    return null;
  }
};
const lsCachedData = preLSCacheProcess();
const indexDBCachedPromise = preIndexDBCacheProcess();

const parseCachedRes = (cachedRes: any, resIdToKeyMap: Record<string, string>): Record<string, IResponse> | null => {
  try {
    if (!Array.isArray(cachedRes)) {
      return null;
    }
    const result = cachedRes.reduce(
      (acc, { status, value }) => {
        if (status === 'fulfilled') {
          const { success, data } = value || {};
          const { originResponse } = data || {};
          const key = resIdToKeyMap?.[originResponse?.resId];
          if (success && key) {
            acc.needUpdate = true;
            acc.dataParsed[key] = {
              from: IBackupDataFrom.LocalCache,
              status: RequestStatus.SUCCESS,
              error: null,
              data: originResponse?.data,
              originResponse: originResponse,
            };
          }
        }
        return acc;
      },
      { needUpdate: false, dataParsed: {} as Record<string, IResponse> },
    );
    const { needUpdate, dataParsed } = result;
    return needUpdate ? dataParsed : null;
  } catch (e) {
    // console.log('parseCachedRes error:', e);
    return null;
  }
};

export const CoreAldDataContext = createContext({});

export const CoreAldDataProvider = ({ children, aldReqConfig }: IProviderParams) => {
  const { groupedByPriority = {}, resIdToKeyMap = {}, keyList = [] } = aldMultiRequestConfig || {};
  const initResponseData = useMemo(() => {
    try {
      let res = keyList.reduce<Record<string, IResponse>>(
        (acc, key) => {
          acc[key] = {
            from: IDataFrom.Default,
            status: RequestStatus.LOADING,
            error: null,
            data: null,
            originResponse: null,
          };
          return acc;
        },
        {},
      );
      const cachedDataParsed = parseCachedRes(lsCachedData, resIdToKeyMap);
      // console.log('cachedDataParsed===', cachedDataParsed);
      if (cachedDataParsed) {
        // 解析localStorage缓存数据, 覆盖初始值
        res = {
          ...res,
          ...cachedDataParsed,
        };
        reportCustom({
          code: 'cacheFirstUseLocalStorage',
          message: '[common][coreAldDataProvider][initResponseData]',
        });
      }
      // console.log("[首屏数据]初始值", res);
      return res;
    } catch (e) {
      return {};
    }
  }, []);
  const [response, setResponse] = useState<Record<string, IResponse>>(initResponseData);

  const updateLocalCacheAsync = (key: string, value: ICacheData, delay = 500) => {
    setTimeout(() => {
      setCacheData(key, value);
    }, delay);
  };

  useMemo(() => {
    const dbCacheFirstHandler = async () => {
      const cachedRes = await indexDBCachedPromise;
      // console.log('[首屏数据]从indexDB中获取数据', cachedRes);
      const dataParsed = parseCachedRes(cachedRes, resIdToKeyMap);
      if (dataParsed) {
        setResponse((pre) => {
          return {
            ...pre,
            ...dataParsed,
          };
        });
        reportCustom({
          code: 'cacheFirstUseIndexDB',
          message: '[common][coreAldDataProvider][dbCacheFirstHandler]',
        });
      }
    };
    if (useCacheFirst && !useLSForCacheFirst) {
      dbCacheFirstHandler();
    }
  }, []);

  useMemo(() => {
    const requestHandler = () => {
      // 组装聚合请求参数
      const priorityKeyArr = Object.keys(groupedByPriority) || [];
      const aldMultiReqParamsArr = priorityKeyArr.map((key) => {
        return getAldMultiRequestParams(groupedByPriority[key]);
      });
      const getAldRequestMap = (priorityArr: IAldReqConfig[]): Record<string, IAldReqConfig> => {
        try {
          return priorityArr.reduce((res: Record<string, IAldReqConfig>, item) => {
            const resId = item?.data?.resId;
            if (resId && resIdToKeyMap[resId]) {
              res[resIdToKeyMap[resId]] = item;
            }
            return res;
          }, {});
        } catch (err) {
          return {};
        }
      };
      // 请求都结束后，看是否存在loading态的资源，设置为error
      const setFinalResponse = (keyList: string[], currentData?: Record<string, IResponse>) => {
        setResponse((pre) => {
          try {
            const currentDataCopied = { ...(currentData || pre) };
            Object.keys(currentDataCopied).forEach((key) => {
              if (keyList.includes(key) && currentDataCopied?.[key]?.status === RequestStatus.LOADING) {
                currentDataCopied[key] = {
                  from: IBackupDataFrom.Empty,
                  status: RequestStatus.ERROR,
                  error: {
                    message: `current ${key} still loading`,
                    originData: null,
                  },
                  data: null,
                  originResponse: null,
                };
              }
            });
            return {
              ...pre,
              ...currentDataCopied,
            };
          } catch (e) {
            return {
              ...pre,
              ...currentData,
            };
          }
        });
      };
      // 请求：会先从预取的promise句柄上取
      const requestParsed = async (params: IAldDataParam, index: number) => {
        if (typeof window?.__pc_index_head_prefetch_list__?.[index]?.then === 'function') {
          return window.__pc_index_head_prefetch_list__[index]
            .then((res: any) => {
              // console.log('[首屏数据]head prefetch的响应');
              if (res?.data?.resultValue) {
                return {
                  success: true,
                  data: res.data.resultValue,
                };
              } else {
                throw Error('prefetch failed');
              }
            })
            .catch((err: any) => {
              // console.log('[首屏数据]head prefetch失败');
              return {
                message: err?.ret?.[0] || err?.message || 'unknown_prefetch',
                originData: err,
              };
            });
        } else {
          // console.log('[首屏数据]发起mtop请求');
          logPerf('mtopRequestStart', Date.now());
          return queryMultiResourcesBase(params);
        }
      };
      // 并行发起聚合请求
      for (let i = 0; i < aldMultiReqParamsArr?.length; i++) {
        const priorityKey = priorityKeyArr?.[i];
        const currentRequestMap = getAldRequestMap(groupedByPriority?.[priorityKey] || []);
        const currentKeyList = Object.keys(currentRequestMap);
        const params = aldMultiReqParamsArr[i];
        let failedRequestMap: Record<string, IAldReqConfig> = {};
        if (params) {
          // 存储错误数据
          let errorData: any = null;
          let keyErrorMap: Record<string, any> = {};
          requestParsed(params, i)
            .then((res) => {
              logPerf('requestEnd', Date.now());
              if (isTrue(res?.success)) {
                const { data = {} } = res || {};
                // 以key为索引吐出数据，代替resId
                const dataParsed: Record<string, IResponse> = {};
                Object.keys(data).forEach((resId) => {
                  const key = resIdToKeyMap?.[resId];
                  if (key) {
                    const { success } = data?.[resId] || {};
                    if (isTrue(success)) {
                      dataParsed[key] = {
                        from: IDataFrom.Ald,
                        status: RequestStatus.SUCCESS,
                        error: null,
                        data: data[resId]?.data,
                        originResponse: data[resId],
                      };
                      // 更新本地缓存
                      updateLocalCacheAsync(resId, {
                        originResponse: data[resId],
                      });
                    } else {
                      failedRequestMap[key] = aldReqConfig[key] || {};
                      keyErrorMap[key] = data?.[resId] || {};
                    }
                  }
                });
                setResponse((pre) => {
                  return {
                    ...pre,
                    ...dataParsed,
                  };
                });
              } else {
                throw res;
              }
            })
            .catch((err) => {
              logPerf('requestEnd', Date.now());
              failedRequestMap = currentRequestMap;
              errorData = err;
              reportCustom({
                code: 'coreAldRequestFailed',
                message: '[common][coreAldDataProvider][requestParsed][catch]',
                sampling: 1,
                c1: JSON.stringify(err),
              });
            })
            .finally(() => {
              const failedRequestKeyList = Object.keys(failedRequestMap);
              // console.log('[首屏数据]失败的资源位列表：', failedRequestKeyList);
              if (failedRequestKeyList.length) {
                const dataParsed: Record<string, IResponse> = {};
                failedRequestKeyList.map((key) => {
                  const config = failedRequestMap[key];
                  commonBackup(config).then((backupRes) => {
                    const { res, from } = backupRes || {};
                    const isBackupSuccess = isTrue(res?.success);
                    // 兜底的错误>接口的错误>通用unknown错误
                    const keyErrorData = isBackupSuccess ? null : res || keyErrorMap?.[key] || errorData || {};
                    const keyErrorDataParsed = keyErrorData ? {
                      message: keyErrorData?.msgInfo || keyErrorData?.message || 'common_backup_empty',
                      originData: keyErrorData?.originData || keyErrorData,
                    } : null;
                    // console.log(`[首屏数据]${key}资源位${from}兜底数据：`, res);
                    reportCustom({
                      code: 'triggerBackup',
                      message: '[common][coreAldDataProvider][commonBackup]',
                      sampling: 1,
                      c1: JSON.stringify({
                        key,
                        from,
                        success: isBackupSuccess,
                        message: keyErrorDataParsed?.message,
                      }),
                    });
                    if (isBackupSuccess) {
                      dataParsed[key] = {
                        from,
                        status: RequestStatus.SUCCESS,
                        error: null,
                        data: res?.data?.originResponse?.data || res?.data?.originResponse,
                        originResponse: res?.data?.originResponse || res?.data,
                      };
                    } else {
                      dataParsed[key] = {
                        from: IBackupDataFrom.Empty,
                        status: RequestStatus.ERROR,
                        error: keyErrorDataParsed,
                        data: null,
                        originResponse: null,
                      };
                    }
                    setFinalResponse(currentKeyList, dataParsed);
                  });
                });
              } else {
                setFinalResponse(currentKeyList);
              }
            });
        } else {
          setFinalResponse(currentKeyList);
        }
      }
    };
    requestHandler();
  }, []);

  // useEffect(() => {
  //   console.log('[首屏数据] context数据发生变化', response);
  // }, [response]);

  useEffect(() => {
    const handleLoad = () => {
      const timeoutId = setTimeout(() => {
        reportPerfCustom();
      }, 1500);
      return () => clearTimeout(timeoutId);
    };
    window.addEventListener('load', handleLoad);
    return () => {
      window.removeEventListener('load', handleLoad);
    };
  }, []);

  return <CoreAldDataContext.Provider value={response}>{children}</CoreAldDataContext.Provider>;
};

export { aldMultiRequestConfig };