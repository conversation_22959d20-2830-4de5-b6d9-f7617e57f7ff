import { createContext, useState } from 'react';
import { mtopRequest } from '../utils/mtop';
import { reportCustom } from '@/utils/jstracker';

export const EvoContext = createContext<IEvoContextValue>({} as any);

export interface IEvoProviderParams {
  childRender: (value: IEvoContextValue) => React.ReactNode;
  pageCode: string;
  moduleCode: string;
}

export enum RequestStatus {
  ERROR = -1,
  LOADING = 0,
  SUCCESS = 1,
}

export interface IEvoBlock {
  blockInfo: {
    blockCode: string;
    dataType: 'array' | 'object';
  };
  items?: any[];
  item?: any;
}

export interface IEvoComponent {
  componentInfo: {
    componentCode: string;
    version: string;
    index: number;
  };
  blocks: IEvoBlock[];
}

export interface IEvoContextValue {
  status: RequestStatus;
  moduleInfo: {
    moduleCode: string;
    version?: string;
  };
  components: IEvoComponent[];
}

export interface IQueryEvoModuleParams {
  pageCode: string;
  moduleCode: string;
}
export const queryEvoModule = (params: IQueryEvoModuleParams) => {
  return mtopRequest({
    api: 'mtop.alibaba.fc.api.maoxland.ContainerFacade.singleView',
    v: '1.0',
    data: {
      projectName: 'PcTaobao',
      responseCode: 'ModuleQuery',
      params: JSON.stringify(params),
    },
  }) as Promise<{ errorCode: string; value: Pick<IEvoContextValue, 'components' | 'moduleInfo'> }>;
};


export const EvoProvider = ({ childRender, pageCode, moduleCode }: IEvoProviderParams) => {
  const [value, setValue] = useState<IEvoContextValue>(() => {
    const key = `${pageCode}_${moduleCode}`;
    const headRequestPromise = window.__pc_index_head_prefetch_map_non_ald__?.[key];
    const requestPromise = headRequestPromise || queryEvoModule({ pageCode, moduleCode });
    requestPromise
      .then((response) => {
        const res = response?.data || {};
        if (res.errorCode === 'SUCCESS' && res.value) {
          setValue({
            status: RequestStatus.SUCCESS,
            moduleInfo: res.value.moduleInfo,
            components: res.value.components,
          });
        } else {
          setValue({
            status: RequestStatus.ERROR,
            moduleInfo: {
              moduleCode,
            },
            components: [],
          });

          reportCustom({
            code: 'module-evo-provider',
            message: `[${pageCode}][${moduleCode}][request][failed]`,
            sampling: 1,
            c1: JSON.stringify(res),
          });
        }
      })
      .catch((err) => {
        setValue({
          status: RequestStatus.ERROR,
          moduleInfo: {
            moduleCode,
          },
          components: [],
        });

        reportCustom({
          code: 'module-evo-provider',
          message: `[${pageCode}][${moduleCode}][request][error]`,
          sampling: 1,
          c1: JSON.stringify(err),
        });
      });

    return {
      status: RequestStatus.LOADING,
      moduleInfo: { moduleCode },
      components: [],
    };
  });

  return <EvoContext.Provider value={value}>{childRender(value)}</EvoContext.Provider>;
};
