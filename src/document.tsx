import { <PERSON><PERSON>, <PERSON><PERSON>, Scripts } from 'ice';
import { system, http } from '@ali/wormhole-context';
import Placeholder from './docComponents/Placeholder';
import HeadPrefetch from './docComponents/HeadPrefetch';
import LoginPrefetch from './docComponents/LoginPrefetch';

// 获取cdnHost
const cdnHost =
  system?.env === "pre" ? "dev.g.alicdn.com" : "g.alicdn.com";

export default function Document() {
  return (
    <html>
      <head>
        <meta charSet="utf-8" />
        <meta
          name="description"
          content="淘宝网 - 亚洲较大的网上交易平台，提供各类服饰、美容、家居、数码、话费/点卡充值… 数亿优质商品，同时提供担保交易(先收货后付款)等安全交易保障服务，并由商家提供退货承诺、破损补寄等消费者保障服务，让你安心享受网上购物乐趣！"
        />
        <meta
          name="keywords"
          content="淘宝,掏宝,网上购物,C2C,在线交易,交易市场,网上交易,交易市场,网上买,网上卖,购物网站,团购,网上贸易,安全购物,电子商务,放心买,供应,买卖信息,网店,一口价,拍卖,网上开店,网络购物,打折,免费开店,网购,频道,店铺"
        />
        <link rel="shortcut icon" href="//www.taobao.com/favicon.ico" />
        <meta
          name="viewport"
          content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no"
        />
        <meta
          name="def-grey"
          content={`${system?.isGray || http?.headers?.get('x-strategy-grey') === 'true' ? 'true' : 'false'}`}
        />
        <meta name="aplus-core" content="aplus.js" />
        <meta name="aplus-core-version" content="1.13.18" />
        <meta name="spm-id" content="tbpcclient.30275656" />
        <Meta />
        <title>首页</title>
        <link rel="dns-prefetch" href="//g.alicdn.com" />
        <link rel="dns-prefetch" href="//gw.alicdn.com" />
        <link rel="dns-prefetch" href="//img.alicdn.com" />
        <link rel="dns-prefetch" href="//gm.mmstat.com" />
        <link rel="dns-prefetch" href="//log.mmstat.com" />
        <Links />
        <script
          src="//g.alicdn.com/mtb/lib-mtop/2.7.2/mtop.js"
          crossOrigin="anonymous"
        />
        <script
          src="//g.alicdn.com/mtb/lib-login/3.1.2/login.js"
          crossOrigin="anonymous"
        />
        <LoginPrefetch />
        <HeadPrefetch />
        <link
          rel="stylesheet"
          href={`//${cdnHost}/main-search/new-search-suggest/2.13.0/bundle.css`}
        />
        <script
          crossOrigin="anonymous"
          src={`//${cdnHost}/main-search/new-search-suggest/2.13.0/bundle.js`}
        />
      </head>
      <body>
        <div id="ice-container">
          <Placeholder />
        </div>
        <script
          defer
          dangerouslySetInnerHTML={{
            // SDKConfig 参考 https://yuque.alibaba-inc.com/datax/jstracker/wp30hf 配置
            __html: `var g_config = window.g_config || {};
              g_config.jstracker2 = g_config.jstracker2 || {};
              g_config.jstracker2.pid = '34626-tracker';`,
          }}
          crossOrigin="anonymous"
        />
        <script
          defer
          src="//g.alicdn.com/jstracker/sdk-assests/5.7.1/index.js"
          crossOrigin="anonymous"
        />
        {/* 个性化sdk */}
        <script
          crossOrigin="anonymous"
          src="//g.alicdn.com/code/npm/@ali/privacy-sdk/0.0.1/index.js"
        />
        <Scripts />
      </body>
    </html>
  );
}
