import { coreAldMultiRequestConfig } from '../coreAldConfig';
import { isTrue } from '../utils/index';

function HeadPrefetch({ isOpen = 'true', aldReqTimeout = 3000 }) {
  return (
    <>
      <script
        dangerouslySetInnerHTML={{
            __html: `
            var $coreAldMultiRequestConfig = ${JSON.stringify(coreAldMultiRequestConfig)}
          `,
          }}
      />
      <script
        dangerouslySetInnerHTML={{
          __html: `
          var $parseAldMultiRequestConfig = function $parseAldMultiRequestConfig(reqConfig) {
            var groupedByPriority = {};
            var keyToResIdMap = {};
            var resIdToKeyMap = {};
            var keyList = Object.keys(reqConfig);
            keyList.forEach(function (key) {
              var _item$data;
              var item = reqConfig[key];
              var priority = (item === null || item === void 0 ? void 0 : item.priority) || 1;
              var resId = item === null || item === void 0 || (_item$data = item.data) === null || _item$data === void 0 ? void 0 : _item$data.resId;
              keyToResIdMap[key] = resId;
              resIdToKeyMap[resId] = key;
              if (!groupedByPriority[priority]) {
                groupedByPriority[priority] = [];
              }
              groupedByPriority[priority].push(item);
            });
            return {
              groupedByPriority: groupedByPriority,
              keyToResIdMap: keyToResIdMap,
              resIdToKeyMap: resIdToKeyMap,
              keyList: keyList
            };
          };
          var $getAldMultiRequestParams = function getAldMultiRequestParams(dataArray) {
            var paramSeparator = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : ",";
            if (!(dataArray !== null && dataArray !== void 0 && dataArray.length)) {
              return null;
            }
            var allKeys = dataArray.reduce(function (keys, item) {
              Object.keys(item.data).forEach(function (key) {
                if (!keys.includes(key)) {
                  keys.push(key);
                }
              });
              return keys;
            }, []);
            var combinedData = dataArray.reduce(function (combined, item) {
              allKeys.forEach(function (key) {
                var value = item.data[key] === undefined ? "" : item.data[key];
                combined[key] = combined[key] === undefined ? value : combined[key] + paramSeparator + value;
              });
              return combined;
            }, {});
            return combinedData;
          };
          var $reportPerfLog = function $reportPerfLog(key, value) {
            try {
              window._perfInfo = window._perfInfo || {};
              window._perfInfo[key] = value;
            } catch (e) {}
          };
          `,
        }}
      />
      {isTrue(isOpen) ? (
        <script
          dangerouslySetInnerHTML={{
            __html: `
            (function () {
                try {
                  var $checkMtopAvailable = function checkMtopAvailable() {
                    return window.lib && window.lib.mtop && window.lib.mtop.request;
                  };
                  var $request = function request(params) {
                    return new Promise(function (resolve, reject) {
                      window.lib.mtop.request(params, resolve, reject);
                    }).catch(function (err) {
                      console.error("head prefetch error", err);
                      return err;
                    });
                  };
                  if (!$checkMtopAvailable()) {
                    console.log("head prefetch fail because mtop is unavailable");
                    return;
                  }
                  function generateTtid() {
                    try {
                      var channel = 1;
                      var appName = 'tbwang';
                      var osType = 'unknown';
                      var appVersion = '1.0.0';
                      var source = 'pc';
                      var platform = window.navigator.platform;
                      if (/Mac/.test(platform)) {
                        osType = 'mac';
                      } else if (/Win/.test(platform)) {
                        osType = 'windows';
                      } else if (/Linux/.test(platform)) {
                        osType = 'linux';
                      }
                      return "".concat(channel, "@").concat(appName, "_").concat(osType, "_").concat(appVersion, "#").concat(source);
                    } catch (e) {
                      return '1@tbwang_unknown_1.0.0#pc';
                    }
                  }
                  var aldMultiRequestConfigData = $parseAldMultiRequestConfig($coreAldMultiRequestConfig);
                  var groupedByPriority = aldMultiRequestConfigData.groupedByPriority;
                  var aldMultiReqParamsArr = Object.keys(groupedByPriority).map(function (key) {
                    return $getAldMultiRequestParams(groupedByPriority[key]);
                  });
                  var ttid = generateTtid();
                  $reportPerfLog("prefetchRequestStart", Date.now());
                  for (var i = 0; i < (aldMultiReqParamsArr === null || aldMultiReqParamsArr === void 0 ? void 0 : aldMultiReqParamsArr.length); i++) {
                    var params = aldMultiReqParamsArr[i];
                    if (params) {
                      window.__pc_index_head_prefetch_list__ = window.__pc_index_head_prefetch_list__ || [];
                      window.__pc_index_head_prefetch_list__.push($request({
                        api: "mtop.tmall.kangaroo.core.service.route.AldLampServiceFixedResV2",
                        v: "1.0",
                        ecode: 0,
                        timeout: ${aldReqTimeout} || 3000,
                        dataType: 'jsonp',
                        valueType: 'original',
                        jsonpIncPrefix: 'tbpc',
                        ttid: ttid,
                        data: {
                          params: JSON.stringify(params)
                        },
                      }));
                    }
                  }
                  window.__pc_index_head_prefetch_map_non_ald__ = window.__pc_index_head_prefetch_map_non_ald__ || {};
                  window.__pc_index_head_prefetch_map_non_ald__.home_homeRight = $request({
                    api: 'mtop.alibaba.fc.api.maoxland.ContainerFacade.singleView',
                    v: '1.0',
                    data: {
                      projectName: 'PcTaobao',
                      responseCode: 'ModuleQuery',
                      params: JSON.stringify({
                        pageCode:"home",
                        moduleCode:"homeRight"
                      }),
                    },
                  });
                } catch (err) {
                  window && window.JSTracker2 && window.JSTracker2.reportCustom && window.JSTracker2.reportCustom({
                    code: 'head-prefetch-error',
                    message: "headPrefetch error" + (err && err.message),
                    sampling: 1
                  });
                }
              })()`,
          }}
        />
      ) : null}
    </>
  );
}

export default HeadPrefetch;
