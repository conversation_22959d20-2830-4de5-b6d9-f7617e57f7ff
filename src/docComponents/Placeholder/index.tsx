import AdaptiveGrid, { SmallBlock } from '@/components/AdaptiveGrid';
import styles from '../../pages/home.module.css';


function Placeholder() {
  return (
    <div className={styles.app}>
      <div className={styles.header} />
      <div className={styles.logoSection} style={{ justifyContent: 'flex-start' }}>
        <div className={styles.logoWrapper}>
          <div
            className={styles.logo}
            role="img"
            aria-label="淘宝客户端Logo"
          />
        </div>
        <div className={styles.searchContainer}>
          <div
            id="layout-search-bar"
            style={{
              borderRadius: '12px',
              boxShadow: 'inset 0 0 0 2px #FF5000',
              display: 'flex',
              flexDirection: 'row',
              flexWrap: 'nowrap',
              overflow: 'hidden',
              width: '896px',
              height: '56px',
              position: 'relative',
            }}
          >
            <div
              style={{
                display: 'flex',
                flexDirection: 'row',
                flexWrap: 'nowrap',
                boxSizing: 'border-box',
                padding: '17px 16px',
                alignItems: 'center',
                width: '792px',
                height: '100%',
              }}
            />
            <div
              style={{
                width: '40px',
                height: '40px',
                top: '8px',
                right: '8px',
                position: 'absolute',
                borderRadius: '8px',
                color: 'transparent',
                backgroundImage: 'url("https://img.alicdn.com/imgextra/i2/O1CN01JpFBRP28A8NqUBATN_!!6000000007891-2-tps-48-48.png")',
                backgroundPosition: 'center',
                backgroundSize: '24px 24px',
                backgroundRepeat: 'no-repeat',
                backgroundColor: 'var(--tb-brand-light-color, #ff5000)',
              }}
            />
          </div>
          <div
            style={{
              display: 'flex',
              flexDirection: 'row',
              flexWrap: 'wrap',
              justifyContent: 'center',
              overflow: 'hidden',
              width: '800px',
              height: '32px',
              marginTop: '16px',
            }}
          />
        </div>
      </div>

      <div className={styles.middleHeader} />
      <AdaptiveGrid>
        <SmallBlock />
        <SmallBlock />
        <SmallBlock />
        <SmallBlock />
      </AdaptiveGrid>
    </div>
  );
}

export default Placeholder;
