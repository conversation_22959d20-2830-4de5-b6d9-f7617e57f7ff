const LoginPrefetch: React.FC = () => {
  return (
    <script
      dangerouslySetInnerHTML={{
        __html: `
          // 检查登录状态并缓存结果
          (function initLoginCheck() {
            try {
              window.isLoginFromLibPromise = window.lib.login.isLoginAsync()
                .catch(err => {
                  console.error('[LoginPrefetch] 登录状态检查失败:', err);
                  return false;
                });
            } catch (err) {
              console.error('[LoginPrefetch] 初始化登录检查失败:', err);
              window.isLoginFromLibPromise = Promise.resolve(false);
            }
          })();
        `,
      }}
    />
  );
};

export default LoginPrefetch;
