
import { useEffect, useRef } from 'react';

/**
 * 只在依赖更新时（跳过首次渲染）执行一次 effect
 * @param effect 副作用函数
 * @param deps 依赖数组
 */
export function useUpdateEffectOnce(effect: () => void | (() => void), deps: any[]) {
    const isFirst = useRef(true);
    const hasRun = useRef(false);

    useEffect(() => {
        if (isFirst.current) {
            isFirst.current = false;
            return;
        }
        if (!hasRun.current) {
            hasRun.current = true;
            effect();
        }
        // 如果有返回值做清理，可扩展
        // eslint-disable-next-line
    }, deps);
}
