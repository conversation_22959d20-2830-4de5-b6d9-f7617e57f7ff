import { useContext } from 'react';
import {
  aldMultiRequestConfig,
  CoreAldDataContext,
  RequestStatus,
} from '../contexts/CoreAldDataContext';

function useCoreAldDataContext<T = any>(key: string) {
  const contextData: Record<
    string,
    { data: T; error: null | Error; status: RequestStatus }
  > = useContext(CoreAldDataContext);

  return (
    contextData?.[key] ||
    (aldMultiRequestConfig?.keyList.includes(key)
      ? { status: RequestStatus.LOADING, error: null, data: null }
      : {
          status: RequestStatus.ERROR,
          error: new Error('target request key not exists'),
          data: null,
        })
  );
}

export default useCoreAldDataContext;
