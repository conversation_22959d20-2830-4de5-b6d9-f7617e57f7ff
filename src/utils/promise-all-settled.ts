export async function allSettled(promises: Promise<any>[]): Promise<
  {
    status: 'fulfilled' | 'rejected';
    value?: any;
    reason?: any;
  }[]
> {
  return new Promise((resolve, reject) => {
    let count = 0;
    const results: {
      status: 'fulfilled' | 'rejected';
      value?: any;
      reason?: any;
    }[] = [];

    if (!Array.isArray(promises)) {
      reject(new TypeError('promises must be an array'));
    }

    if (promises.length === 0) {
      resolve([]);
    }

    promises.forEach((promise, index) => {
      Promise.resolve(promise).then(
        (value) => {
          results[index] = { status: 'fulfilled', value };
          checkFinished();
        },
        (reason) => {
          results[index] = { status: 'rejected', reason };
          checkFinished();
        },
      );
    });

    function checkFinished() {
      count++;
      if (count >= promises.length) {
        resolve(results);
      }
    }
  });
}
