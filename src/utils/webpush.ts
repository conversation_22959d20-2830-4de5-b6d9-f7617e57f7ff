import { isTrue } from '.';
// import { getQuery } from './url';

export const PRE_CDN_ADDRESS = 'https://hudong.alicdn.com/api/pre/data/v3/c45746794e4a4de99af2b9fb4f65e0d2.js';
export const ONLINE_CDN_ADDRESS = 'https://hudong.alicdn.com/api/online/data/v3/c45746794e4a4de99af2b9fb4f65e0d2.js';
const APP_KEY = 'H5_5V06SHN0y63vy1';

export type MTConfig = Record<
    'browserCfg' |
    'siteMsgHostCfg' |
    'commonCfg' |
    'fatigueCfg' |
    'siteMsgRedirectionConfig' |
    'hostCfg',
    any[]
>;
function checkHostAllowlist(hostConfig: Array<any>) {
    try {
        return !!hostConfig?.find(conf => conf.hostname && conf.hostname === window.location.hostname);
    } catch (e) {
        return false;
    }
}

export function isSitepushAvailable(config: MTConfig) {
    try {
        // 本地环境默认打开
        // if (window.location.hostname === 'localhost') {
        //     return true;
        // }
        const { siteMsgHostCfg } = config;
        const disabled = !config?.commonCfg?.length || isTrue(config.commonCfg[0]?.disable);
        const inHostAllowList = checkHostAllowlist(siteMsgHostCfg);

        return inHostAllowList && !disabled;
    } catch (e) {
        return false;
    }
}

// 标记是否已加载过iframe
let isIframeLoaded = false;

export function loadIframe(url) {
    try {
        const iframe = document.createElement('iframe');
        iframe.src = url;
        iframe.style.display = 'none';
        // iframe.onload = () => {
        //     console.log('iframe 加载成功');
        // };
        iframe.onerror = (e) => {
            console.error('iframe 加载失败', e);
        };
        document.body.appendChild(iframe);
        return iframe;
    } catch (err) {
        console.error('创建iframe异常', err);
        return null;
    }
}

function getWebPushIframeUrl() {
    const originUrl = encodeURIComponent(window.location.origin);
    // const appKey = getQuery('appKey');
    // return `https://pre-push-v1.taobao.com/index.html?webpush=true&originUrl=${originUrl}${appKey ? `&appKey=${appKey}` : ''}`;
    return `https://www.taobao.com/pushiframe.html?originUrl=${originUrl}&appKey=${encodeURIComponent(APP_KEY)}`;
}

export function loadWebPushIframe() {
    window.addEventListener('load', async () => {
        if (isIframeLoaded) {
            return;
        }
        try {
            const cdnUrl = window.location.hostname.indexOf('pre-') >= 0 ? PRE_CDN_ADDRESS : ONLINE_CDN_ADDRESS;
            const res = await fetch(cdnUrl);
            const data = await res.json();
            if (isSitepushAvailable(data)) {
                loadIframe(getWebPushIframeUrl());
                isIframeLoaded = true;
                console.log('webpush触发加载');
            } else {
                console.log('webpush开关未打开或域名不在白名单');
            }
        } catch (err) {
            console.error('webpush开关请求异常', err);
        }
    });
}


