import type { IEvoContextValue } from '../contexts/EvoContext';

interface GetValueFromEvoDataParams {
  components: IEvoContextValue['components'];
  componentCode: string;
  blockCode: string;
}

export const getValueFromEvoData = ({ components, componentCode, blockCode }: GetValueFromEvoDataParams) => {
  const _targetComponent = components?.find((_c) => _c?.componentInfo?.componentCode === componentCode);
  if (_targetComponent?.blocks?.length) {
    const _targetBlock = _targetComponent.blocks?.find((_b) => _b?.blockInfo?.blockCode === blockCode);
    if (_targetBlock?.blockInfo?.dataType === 'array' && _targetBlock.items) {
      return _targetBlock.items;
    } else {
      return _targetBlock?.item;
    }
  }
  return null;
};
