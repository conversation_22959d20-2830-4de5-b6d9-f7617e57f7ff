import { reportCustom } from './jstracker';

/**
 * 存储数据到本地存储
 * @param key 存储的键
 * @param value 存储的值
 */
export const setLocalStorageItem = (key: string, value: any) => {
  try {
    window.localStorage.setItem(key, JSON.stringify(value));
    return true;
  } catch (error) {
    reportCustom({
      code: 'localStorage-failed',
      message: '[utils][storage][localStorage][set error]',
      sampling: 1,
      c1: JSON.stringify(error?.message || 'set localStorage onerror triggered'),
    });
    return false;
  }
};

/**
 * 从本地存储获取数据
 * @param key 存储的键
 * @returns 存储的值或null（如果键不存在或解析失败）
 */
export const getLocalStorageItem = (key: string): any | null => {
  try {
    const theValue = window.localStorage.getItem(key);
    return theValue ? JSON.parse(theValue) : null;
  } catch (error) {
    reportCustom({
      code: 'localStorage-failed',
      message: '[utils][storage][localStorage][get error]',
      sampling: 1,
      c1: JSON.stringify(error?.message || 'get localStorage onerror triggered'),
    });
    return null;
  }
};
