export function getCookie(e: string) {
  let n = document.cookie.match(`(?:^|;)\\s*${e}=([^;]*)`);
  return n && n[1] ? decodeURIComponent(n[1]) : '';
}

export function isLogin() {
  let e = getCookie('dnk') || getCookie('_nk_') || getCookie('tracknick'),
    n = getCookie('_l_g_'),
    t = getCookie('lgc');
  return !!((n && e) || t);
}

export function getNick() {
  let e = getCookie('dnk') || getCookie('_nk_'),
    n = getCookie('lgc'),
    t = e || n;
  return t && (t = fromUnicode(t).replace(/[<>%&;\\'"]/g, '')), t;
}

export function getAvatar() {
  let e = '//img.alicdn.com/imgextra/i2/O1CN01JxmiD929ugCNubQg3_!!6000000008128-0-tps-200-200.jpg',
    n = getNick();
  return n
    ? `//wwc.alicdn.com/avatar/getAvatar.do?userNick=${n}&_input_charset=UTF-8&width=80&height=80&type=sns`
    : e;
}

function fromUnicode(e: string) {
  return e.replace(/\\u([a-f\d]{4})/gi, (e, n) => {
    return String.fromCharCode(parseInt(n, 16));
  });
}
