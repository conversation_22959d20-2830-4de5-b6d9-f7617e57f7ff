import { mtopRequest } from './mtop';

export interface IAldDataParam {
  // 阿拉丁资源位ID
  resId: string;
  // 阿拉丁业务身份ID
  bizId?: string;
  // 阿拉丁去重域ID
  _pvuuid?: string;
  // 自定义兜底 key 列表
  backupParams?: string;
  pageIndex?: number;
  pageSize?: number;
  // 业务自定义
  [key: string]: any;
}

export interface IAldReqConfig {
  // 是否开启本地缓存
  openLocalCache: boolean;
  // 是否启用ald兜底
  openAldBottom: boolean;
  // 斑马兜底资源位tag
  staticBottomTags?: string;
  // 是否可优先使用缓存数据
  useCacheFirst: boolean;
  // 请求优先级，ald聚合请求会以相同的priority为一批进行请求
  priority: number;
  // ald请求的参数
  data: IAldDataParam;
}

export interface IAldReqParsedRes {
  groupedByPriority: Record<string, IAldReqConfig[]>;
  keyToResIdMap: Record<string, string>;
  resIdToKeyMap: Record<string, string>;
  keyList: string[];
}
export const parseAldMultiRequestConfig = (
  reqConfig: Record<string, IAldReqConfig>,
): IAldReqParsedRes => {
  const groupedByPriority: Record<number, IAldReqConfig[]> = {};
  const keyToResIdMap: Record<string, string> = {};
  const resIdToKeyMap: Record<string, string> = {};
  const keyList = Object.keys(reqConfig);
  keyList.forEach((key) => {
    const item = reqConfig[key];
    // 没有设置时，默认priority为1
    const priority = item.priority || 1;
    const { resId } = item.data;
    keyToResIdMap[key] = resId;
    resIdToKeyMap[resId] = key;
    if (!groupedByPriority[priority]) {
      groupedByPriority[priority] = [];
    }
    groupedByPriority[priority].push(item);
  });
  // console.log('groupedByPriority=', groupedByPriority);
  // console.log('keyToResIdMap=', keyToResIdMap);
  // console.log('resIdToKeyMap=', resIdToKeyMap);
  return {
    groupedByPriority,
    keyToResIdMap,
    resIdToKeyMap,
    keyList,
  };
};

export const getAldMultiRequestParams = (
  dataArray: IAldReqConfig[],
  paramSeparator = ',',
): IAldDataParam | null => {
  if (!dataArray?.length) {
    return null;
  }
  const allKeys = dataArray.reduce<string[]>((keys, item) => {
    Object.keys(item.data).forEach((key) => {
      if (!keys.includes(key)) {
        keys.push(key);
      }
    });
    return keys;
  }, []);

  const combinedData = dataArray.reduce<IAldDataParam>((combined, item) => {
    allKeys.forEach((key) => {
      const value = item.data[key] === undefined ? '' : item.data[key];
      combined[key] =
        combined[key] === undefined
          ? value
          : `${combined[key]}${paramSeparator}${value}`;
    });
    return combined;
  }, {} as IAldDataParam);

  // console.log('getAldMultiRequestParams=', combinedData);
  return combinedData;
};

export interface IQueryMultiResourceParams {
  /**
   * 资源位 ID 列表
   */
  resIdList: number[];
  /**
   * 解决方案 ID
   *
   * @note 不传默认使用 443
   */
  bizId?: string;
  /**
   * 扩展参数
   */
  [key: string]: any;
}

/**
 * 请求多个资源位数据
 * @param params 请求参数
 * @param params.resIdList 资源位ID列表
 * @param options 配置
 * @returns
 */
export const queryMultiResources = async <IData extends Record<number, object>>(
  params: IQueryMultiResourceParams,
): Promise<{
  success: boolean;
  /**
   * 请求结果
   */
  data?: IData;
  /**
   * 是否是打底数据
   */
  bottom?: boolean;
  /**
   * 错误信息
   */
  message?: string;
  /**
   * trace
   */
  traceId?: string;
}> => {
  try {
    const { resIdList, bizId, ...extParams } = params || {};
    const paramsParsed = {
      resId: resIdList.join(','),
      bizId: bizId || '443',
      ...extParams,
    };
    return await queryMultiResourcesBase(paramsParsed);
  } catch (err) {
    return {
      success: false,
      message: err.message || err,
    };
  }
};

export const queryMultiResourcesBase = async <
  IData extends Record<number, object>,
>(
  params: IAldDataParam,
): Promise<{
  success: boolean;
  /**
   * 请求结果
   */
  data?: IData;
  /**
   * 是否是打底数据
   */
  bottom?: boolean;
  /**
   * 错误信息
   */
  message?: string;
  /**
   * trace
   */
  traceId?: string;
}> => {
  try {
    const res = await mtopRequest({
      api: 'mtop.tmall.kangaroo.core.service.route.AldLampServiceFixedResV2',
      data: {
        params: JSON.stringify(params),
      },
      needLogin: false,
    });
    const { resultValue } = res || {};
    // 返回结果
    return {
      success: true,
      data: resultValue as IData,
      bottom: false,
      traceId: res?.traceId,
    };
  } catch (err) {
    return {
      success: false,
      message: err.message || err,
    };
  }
};
