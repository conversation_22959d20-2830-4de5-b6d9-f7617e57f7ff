import { obj2String, string2Obj } from './';

const getLocationUrl = () => {
  return typeof location === 'object' ? location.href : '';
};

const URL_REG = new RegExp(
  '^([a-z0-9-]+:)?' + // protocol
    '[/]{2}' + // slash x 2
    '(?:([^@/:?]+)(?::([^@/:]+))?@)?' + // username:password@
    '([^:/?#]+)' + // hostname
    '(?:[:]([0-9]+))?' + // port
    '([/][^?;]*)?' + // pathname
    '(?:[?]([^#]*))?' + // search
    '([#][^#]*)?$', // hash
  'i',
);

const isURL = (str: string) => {
  return !!str?.match(URL_REG);
};

export const getUrlInfo = (_url?: string) => {
  const url = _url || getLocationUrl();
  const matches = url.match(URL_REG) || [];

  const info = {
    protocol: '',
    hostname: '',
    port: '',
    host: '',
    pathname: '',
    search: '',
    hash: '',
  };
  info.protocol =
    matches[1] || (typeof location === 'object' ? location.protocol : '');
  info.hostname = matches[4];
  info.port = matches[5] || '';
  info.host = info.hostname + (info.port ? `:${info.port}` : '');
  info.pathname = matches[6] || '/';
  info.search = matches[7] || '';
  info.hash = matches[8] || '';

  return info;
};

/**
 * @name getQueries
 * @description 获取URL所有参数
 * @param { String } url URL地址，默认为location.href
 * @returns { Object }
 */
export const getQueries = (_url = ''): { [key: string]: string } => {
  const url = _url || getLocationUrl();
  if (!isURL(url)) {
    console.error('not url', url);
    return {};
  }

  const search = getUrlInfo(url).search.replace(/^\?/, '');
  if (!search) {
    return {};
  }

  const searchParams = string2Obj(search);
  return searchParams;
};

/**
 * @name getQuery
 * @description 根据name获取URL参数
 * @param { String } name queryName
 * @param { String } url URL地址，默认为location.href
 * @returns { String }
 */
export const getQuery = (name: string, _url = ''): string => {
  if (!name) {
    return '';
  }
  const url = _url || getLocationUrl();
  const searchParams = getQueries(url);
  return searchParams[name] || '';
};

/**
 * @name addQuery
 * @description 添加URL参数
 * @param { Object } paramObj 参数配置
 * @param { String } url URL地址，默认为location.href
 * @returns { String }
 */
export const addQuery = (
  paramObj: { [key: string]: number | boolean | string },
  _url = '',
) => {
  const url = _url || getLocationUrl();
  if (!isURL(url)) {
    console.error('not url', url);
    return '';
  }
  const urlInfo = getUrlInfo(url);
  const searchParams = Object.assign(
    string2Obj(urlInfo.search.replace(/^\?/, '')),
    {
      ...paramObj,
    },
  );
  const newSearch = obj2String(searchParams);
  return `${urlInfo.protocol}//${urlInfo.host}${urlInfo.pathname}?${newSearch}${urlInfo.hash}`;
};

/**
 * @name rmQuery
 * @description 根据name去除URL参数
 * @param { String } name queryName，多个以英文逗号隔开
 * @param { String } url URL地址，默认为location.href
 * @returns { String }
 */
export const rmQuery = (name: string, _url = '') => {
  const url = _url || getLocationUrl();
  if (!isURL(url)) {
    console.error('not url', url);
    return '';
  }
  if (!name) {
    return url;
  }
  const nameList = name.replace(/\s/g, '').split(',');
  const urlInfo = getUrlInfo(url);
  const searchParams = string2Obj(urlInfo.search.replace(/^\?/, ''));
  nameList.forEach((item) => {
    delete searchParams[item];
  });
  const newSearch = obj2String(searchParams);
  return `${urlInfo.protocol}//${urlInfo.host}${urlInfo.pathname}?${newSearch}${urlInfo.hash}`;
};
