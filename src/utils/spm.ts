const spm = {
  spmA: '',
  spmB: '',
};

export function getPageSpm(spmC = '', spmD = '') {
  if (!spm.spmA || !spm.spmB) {
    getPageSpmAB();
  }
  return `${spm.spmA}.${spm.spmB}.${spmC}.${spmD}`;
}

export function getPageSpmAB() {
  if (spm.spmA && spm.spmB) {
    return `${spm.spmA}.${spm.spmB}`;
  }

  if (
    window &&
    window.goldlog &&
    window.goldlog.spm_ab &&
    window.goldlog.spm_ab.length === 2
  ) {
    spm.spmA = window.goldlog.spm_ab[0];
    spm.spmB = window.goldlog.spm_ab[1];
  } else if (document && document.querySelector('meta[name="spm-id"]')) {
    const _spm_ab = document
      .querySelector('meta[name="spm-id"]')!
      .getAttribute('content');
    if (_spm_ab && _spm_ab.split('.').length === 2) {
      spm.spmA = _spm_ab.split('.')[0];
      spm.spmB = _spm_ab.split('.')[1];
    }
  }

  return `${spm.spmA}.${spm.spmB}`;
}
