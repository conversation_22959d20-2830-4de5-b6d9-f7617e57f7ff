interface ImageOptimizationOptions {
  width?: number;
  height?: number;
  quality?: number;
  enableWebp?: boolean;
  compressSize?: 'small' | 'middle' | 'large' | 'default';
  webpSupport?: boolean;
}

const sizeMap = {
  small: '360',
  middle: '460',
  large: '580',
  default: '360',
};

// 添加 HTTPS 处理
const ensureHttps = (url: string): string => {
  if (!url) return '';
  // 处理以 // 开头的 URL
  if (url.startsWith('//')) {
    return `https:${url}`;
  }
  // 处理 http:// 开头的 URL
  if (url.startsWith('http://')) {
    return url.replace('http://', 'https://');
  }
  // 如果已经是 https 或其他情况，直接返回
  return url;
};

// 清理URL
const cleanUrl = (url: string): string => {
  return url?.replace?.(/(_0x0|_sum\.jpg)/g, '') || url;
};

// 添加尺寸参数
const addSizeParams = (url: string, options: ImageOptimizationOptions): string => {
  const { width, height, compressSize = 'small' } = options;

  if (compressSize === 'default') {
    if (width && height) {
      return `${url}_${width}x${height}`;
    } else {
      const _size = sizeMap[compressSize];
      return `${url}_${_size}x${_size}`;
    }
  }
  const size = sizeMap[compressSize];

  return `${url}_${size}x${size}`;
};

// 添加质量参数
const addQualityParam = (url: string, options: ImageOptimizationOptions): string => {
  const { quality = 90 } = options;
  return `${url}q${quality}.jpg`;
};

// 添加WebP支持
const addWebpSupport = (url: string, options: ImageOptimizationOptions): string => {
  const { enableWebp = true, webpSupport = false } = options;
  if (enableWebp && webpSupport) {
    return `${url}_.webp`;
  }
  return url;
};

export const optimizeImage = (originalSrc: string, options: ImageOptimizationOptions = {}): string => {
  if (!originalSrc) return '';

  // 首先确保 HTTPS
  let optimizedUrl = ensureHttps(originalSrc);

  // 然后进行其他优化
  optimizedUrl = cleanUrl(optimizedUrl);
  optimizedUrl = addSizeParams(optimizedUrl, options);
  optimizedUrl = addQualityParam(optimizedUrl, options);
  optimizedUrl = addWebpSupport(optimizedUrl, options);

  return optimizedUrl;
};
