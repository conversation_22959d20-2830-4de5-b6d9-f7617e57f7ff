export const isString = (obj: unknown) => typeof obj === 'string';
export const isValid = (val: unknown) =>
  val !== undefined && val !== null && val !== '' && String(val) !== 'NaN';

export const isTrue = (flag: unknown) =>
  !!(flag === true || flag === 'true' || flag === '1' || flag === 1);
export const transferStringToFix2 = (input: string | number) => {
  try {
    const _input = Number(input);
    if (!isNaN(_input)) {
      return _input.toFixed(2);
    } else {
      return input;
    }
  } catch (e) {
    return input;
  }
};

export const formatArrToObj = (arr: any[], key: string, valueKey?: string) => {
  if (!arr?.length) {
    return null;
  }
  return arr.reduce((acc, curr) => {
    const id = curr[key];
    if (id) {
      acc[id] = valueKey ? curr[valueKey] : curr;
    }
    return acc;
  }, {});
};

/**
 * @method obj2String
 * @param {Object} obj
 * @param {Boolean} needEncode
 * @param {String} separator
 * @returns {String}
 */
export const obj2String = (
  obj: { [key: string]: string | number | boolean },
  needEncode = true,
  separator = '&',
) => {
  const arr: string[] = [];
  for (const k in obj) {
    if (needEncode) {
      arr.push(`${encodeURIComponent(k)}=${encodeURIComponent(obj[k])}`);
    } else {
      arr.push(`${k}=${obj[k]}`);
    }
  }
  return arr.join(separator);
};

/**
 * @method string2Obj
 * @param {String} str
 * @param {Boolean} needEncode
 * @param {String} separator
 * @returns {Object}
 */
export const string2Obj = (str = '', needDecode = true, separator = '&') => {
  if (!str) {
    return {};
  }
  const result: Record<string, string> = {};
  const splitStr = str.split(separator);
  for (let i = 0; i < splitStr.length; i++) {
    const s = splitStr[i];
    const splitKV = s.split('=');
    if (splitKV.length !== 2) continue;
    const key = needDecode ? safeDecodeURIComponent(splitKV[0]) : splitKV[0];
    const val = needDecode ? safeDecodeURIComponent(splitKV[1]) : splitKV[1];
    result[key] = val;
  }
  return result;
};

export const safeDecodeURIComponent = (str: string) => {
  try {
    return decodeURIComponent(str);
  } catch {
    return str;
  }
};

export const isLoginFromLib = (() => {
  try {
    return window.lib.login.isLogin();
  } catch (err) {
    return false;
  }
})();

export const isLoginFromLibAsync = async () => {
  try {
    const res = await window.lib.login.isLoginAsync();
    return !!res?.data;
  } catch (err) {
    return false;
  }
};

// 不要加“?”
export const HomeUrlHref = '//i.taobao.com/my_itaobao';

let cachedUA: string | null = null;
export const getUA = (): string => {
  if (cachedUA !== null) {
    return cachedUA;
  }
  try {
    cachedUA =
      typeof navigator === 'undefined' ? '' : navigator.userAgent || '';
  } catch (e) {
    console.warn('Failed to retrieve user agent:', e);
    cachedUA = '';
  }
  return cachedUA;
};
export const detectOS = () => {
  try {
    const ua = getUA().toLowerCase();
    let osType = 'unknown';
    if (/windows|win32|win64|wow32|wow64/.test(ua)) {
      osType = 'windows';
    } else if (/macintosh|mac os x/.test(ua)) {
      osType = 'mac';
    } else if (/linux/.test(ua)) {
      osType = 'linux';
    } else if (/android/.test(ua)) {
      osType = 'android';
    } else if (/ios|iphone|ipad|ipod/.test(ua)) {
      osType = 'ios';
    }
    return osType;
  } catch (e) {
    return 'unknown';
  }
};

let isInitTBPCClientLayout = false;
export const setTBPCClientLayout = () => {
  if (isInitTBPCClientLayout) {
    return;
  }
  const isWin = detectOS() === 'windows';
  // 自定义滚动条的 CSS
  const scrollBarCss = isWin
    ? `
  body::-webkit-scrollbar {
      width: 8px;
      height: 8px;
  }
  body::-webkit-scrollbar-track {
      background-color: #fff;
  }
  body::-webkit-scrollbar-thumb {
      background-color: #ccc;
      border-radius: 4px;
  }
  body::-webkit-scrollbar-thumb:hover {
      background: #888;
  }
`
    : '';
  const styleElHead = document.createElement('style');
  styleElHead.textContent = `
            ${scrollBarCss}
        `;
  document.head.appendChild(styleElHead);
  isInitTBPCClientLayout = true;
};
