import { ttid } from './ttid';

/**
 * 发起Mtop网络请求。
 *
 * 和原版的@ali/universal-mtop相比，区别有：
 * 1. 增加了对安全接入的自动处理，接入安全只需要设置data.asac，无需其他任何处理；
 * 2. 支持使用服务端HSF接入安全的逻辑，只需要传入serverSideMtee=true即可，此时会关闭Mtop安全校验；
 * 3. 优化了请求因未登录导致失败时自动拉起登录的逻辑；
 * 4. 增加了needRetry参数并默认置为true，用于在请求失败时自动进行重试。
 *
 * */
export const mtopRequest = (config: MtopConfig) => {
    if (!window?.lib?.mtop?.request) {
        return Promise.reject(new Error('Mtop不存在'));
    }
    const Mtop = window.lib.mtop;

    return Mtop.request({
        v: '1.0',
        ecode: config.needLogin ? 1 : 0,
        timeout: 5000,
        dataType: 'jsonp',
        valueType: 'original',
        jsonpIncPrefix: 'tbpcElectronClient',
        ttid: ttid,
        ...config,
        data: {
            ...config.data,
        },
    })
        .then((response: any) => {
            if (response?.data) {
                return Object.assign({}, response.data, { traceId: response.traceId });
            }
            return response;
        })
        .catch((errorResponse: any) => {
            if (config.needRetry !== false) {
                return mtopRequest({ ...config, needRetry: false });
            } else {
                throw errorResponse;
            }
        });
};

export interface MtopConfig {
    /**
     * mtop api
     *
     * mtop接口平台：https://max.m.taobao.com/mtop/myapi?biz_domain=ali
     * */
    api: string;
    /**
     * 接口版本，默认值为`1.0`，通常无需填写，除非后端有明确要求使用其他版本。
     * */
    v?: string;
    /**
     * mtop请求参数，需要根据后端接口进行相应的传值。无需参数的场景下可不填写。
     *
     * 如果接口需要安全校验，则必须填写data.asac的值。
     * */
    data?: {
        [field: string]: any;
        /**
         * mtop安全码。使用mtop方式接入活动反作弊时，必须填写。
         *
         * 该参数只需要在这里填写即可，其他与安全接入相关的参数已经自动处理，无需关注。
         * */
        asac?: string;
    };
    /**
     * 若该接口需要登录才能返回数据，则自动拉起登录。项目依赖中需要引入`@ali/universal-user`。
     * */
    needLogin?: boolean;
    /**
     * 请求失败时是否需要自动重试一次，默认为true。
     * */
    needRetry?: boolean;
    /**
     * 客户端请求参数，请求需要登录的接口为1，反之为0，该参数默认值为0。
     * */
    ecode?: number;
    /**
     * 标识请求应用。默认值为'12574478'，通常无需填写。
     * */
    appKey?: string;
    /**
     * 标识请求类型，jsonp请求只有GET类型。
     * */
    type?: 'GET' | 'POST';
    /**
     * 标示请求方式，`json`为 CORS 跨域请求。
     *
     * 默认值为`json`，
     * */
    dataType?: 'jsonp' | 'json';
    /**
     * 更改服务端返回结果中的布尔和数字类型（original时值为 true 和 1。string时值为 "true" 和 "1" ）。
     *
     * 不设置时，结果值以原有的 dataType 为准。(json 时为原始类型，jsonp 是为字符串型的布尔和数字)
     * */
    valueType?: 'original' | 'string';
    /**
     * 请求超时时间，默认为4000。
     * */
    timeout?: number;
    /**
     * 为请求方式为json的请求附加额外请求头。注意，该项设置对jsonp请求无效。
     *
     * 如果你填写了data.asac，则此参数会被自动添加asac的值，无需手动填写。
     * */
    ext_headers?: { [field: string]: string };
    /**
     * 为请求的search部分附加额外参数。
     *
     * 如果你填写了data.asac，则此参数会被自动添加asac的值，无需手动填写。
     * */
    ext_querys?: { [field: string]: string };
    /**
     * 此参数无需手动设置。此参数的值取决于你是否使用了安全码data.asac。
     *
     * 标识是否启用安全校验参数，1为开启。
     * */
    isSec?: number;
    /**
     * 强制使用客户端入口发起Mtop请求。通常无需手动设置。
     * */
    WindVaneRequest?: boolean;
    /**
     * 强制使用H5入口发起Mtop请求。通常无需手动设置。
     * */
    H5Request?: boolean;
    /**
     * 客户端请求参数，通常无需填写。详见[登录请求介绍](https://yuque.antfin.com/mtbsdkdocs/mtopjssdkdocs/icgqle)
     * */
    sessionOption?: string;
    /**
     * 是否接入防爬能力。详见[防爬能力介绍](https://yuque.antfin.com/mtbsdkdocs/mtopjssdkdocs/xn0yzx)
     * */
    AntiCreep?: boolean;
    /**
     * 是否接入防刷能力。详见[防刷能力介绍](https://yuque.antfin.com/mtbsdkdocs/mtopjssdkdocs/xbqs7l)
     * */
    AntiFlood?: boolean;
    /**
     * 通常无需填写。
     *
     * 可更改jsonp callback组合名称。
     * 例如：设置`weex`时，jsonp callback名称将变成mtopjsonpweex1(后面数字位递增);
     * */
    jsonpIncPrefix?: string;
    /**
     * 使用HSF形式安全接入时，设置为true，否则请勿传入此参数。使用前请务必与后端确认使用HSF安全接入方式。
     *
     * 使用此参数后，请不要传入data.asac(如果传入将被忽略，防止出现重复调用)，会使用服务端设置的安全码。
     * */
    serverSideMtee?: boolean;
    ttid?: string;
}
