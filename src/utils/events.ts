/**
 * 发送淘金币任务事件
 * @param type 任务类型，必须是 taskList 中的有效选项
 * @param from 事件来源标识
 */
export const dispatchTaoCoinTaskEvent = (type: string, from: string = 'client') => {
  const validTypes = ['search', 'guang', 'bbs', 'live'];

  if (!validTypes.includes(type)) {
    console.warn(`Invalid task type: ${type}. Expected one of: ${validTypes.join(', ')}`);
    return;
  }

  const myEvent = new CustomEvent('TAOCOIN_TASK', {
    detail: {
      type,
      from,
    },
  });

  window.dispatchEvent(myEvent);
};