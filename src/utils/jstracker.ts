// 相关code在https://jstracker.alibaba-inc.com/dawnnew/#/project/30885-tracker?type=3申请
export const reportCustom = (params: {
  code?: string;
  message?: string;
  success?: boolean;
  sampling?: number;
  type?: string;
  d1?: string;
  d2?: string;
  c1?: string;
  c2?: string;
  c3?: string;
  c4?: string;
  c5?: string;
}) => {
  const jsTracker2 = window?.JSTracker2;
  if (!jsTracker2) {
    return;
  }
  try {
    if (typeof jsTracker2?.reportCustom === 'function') {
      jsTracker2?.reportCustom(params);
    } else if (typeof jsTracker2?.push === 'function') {
      params.type = 'manual';
      jsTracker2?.push(params);
    }
  } catch (e) {
    console.error(e);
  }
};

export const reportError = (params: {
  message: string;
  fileName?: string;
  lineNumber?: string;
  columnNumber?: string;
  stack?: string;
  sampling?: number;
}) => {
  try {
    if (typeof window?.JSTracker2.reportError === 'function') {
      window.JSTracker2.reportError(params);
    }
  } catch (e) { }
};

export const logPerf = (key: string, value: number) => {
  window?.$reportPerfLog?.(key, value);
};

export const reportPerfCustom = () => {
  try {
    const { prefetchRequestStart, mtopRequestStart, requestEnd, pageIndexRenderEnd } = window._perfInfo || {};
    let prefetchDuration = requestEnd - prefetchRequestStart;
    let mtopDuration = requestEnd - mtopRequestStart;
    if (isNaN(prefetchDuration)) {
      prefetchDuration = -1;
    }
    if (isNaN(mtopDuration)) {
      mtopDuration = -1;
    }
    const perf = {
      name: 'firstScreenPerf',
      isPrefetch: prefetchRequestStart ? 1 : 0,
      prefetchDuration,
      mtopDuration,
      pageIndexAfterRender: -1,
      dnsTime: -1,
      connectTime: -1,
      ttfb: -1,
      domContentLoadedTime: -1,
      loadTime: -1,
    };
    if (typeof performance !== 'undefined' && typeof performance.getEntriesByType === 'function') {
      const navTimings = performance.getEntriesByType('navigation');
      if (navTimings.length) {
        const navTiming = navTimings[0] as any;
        const dnsTime = navTiming?.domainLookupEnd - navTiming?.domainLookupStart;
        const connectTime = navTiming?.connectEnd - navTiming?.connectStart;
        const ttfb = navTiming?.responseStart - navTiming?.requestStart;
        const domContentLoadedTime = navTiming?.domContentLoadedEventEnd - navTiming?.domainLookupStart;
        const loadTime = navTiming?.loadEventEnd - navTiming?.domainLookupStart;
        const pageIndexAfterRender = pageIndexRenderEnd - navTiming?.domainLookupStart;
        perf.dnsTime = dnsTime >= 0 ? Math.round(dnsTime) : -1;
        perf.connectTime = connectTime >= 0 ? Math.round(connectTime) : -1;
        perf.ttfb = ttfb >= 0 ? Math.round(ttfb) : -1;
        perf.domContentLoadedTime = domContentLoadedTime >= 0 ? Math.round(domContentLoadedTime) : -1;
        perf.loadTime = loadTime >= 0 ? Math.round(loadTime) : -1;
        perf.pageIndexAfterRender = pageIndexAfterRender >= 0 ? Math.round(pageIndexAfterRender) : -1;
      }
    }
    console.log('[perf]:', perf);
    if (typeof window?.JSTracker2?.push === 'function') {
      window.JSTracker2.push({
        type: 'perf',
        msg: JSON.stringify(perf),
        sampling: 0.1,
        name: 'performance',
      });
    }
  } catch (e) { }
};