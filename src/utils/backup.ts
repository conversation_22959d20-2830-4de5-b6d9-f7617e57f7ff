import jsonp from 'jsonp';
import type { IAldDataParam, IAldReqConfig } from './ald-request';
import { isTrue } from './';
import { getCacheData } from './cache';
import { getQueries } from './url';

interface IRes {
    success: boolean;
    data: any;
}

export enum IBackupDataFrom {
    LocalCache = 'localCache',
    AldBackup = 'aldBackup',
    Static = 'static',
    Empty = 'empty',
}

const parseBackupParams = (backupParams = '') => {
    try {
        if (!backupParams) {
            return {};
        }
        const queryObj = getQueries();
        const backupParamsArr = backupParams?.split?.(',') || [];
        const backupParamsObj: Record<string, string> = {};
        backupParamsArr.forEach((key) => {
            backupParamsObj[key] = queryObj?.[key] || '';
        });
        return backupParamsObj;
    } catch (e) {
        return {};
    }
};

const getNestedProperty = (obj: any, path: string) => {
    try {
        const properties = path.split('.');
        return properties.reduce((current, property) => {
            return current && property in current ? current[property] : undefined;
        }, obj);
    } catch (err) {
        return;
    }
};

const getBottomDataUrl = (params: IAldDataParam): string => {
    try {
        const { resId = '', backupParams = '' } = params || {};
        const appIdArr = resId?.split(',');
        // todo 当前场景固定ald-lamp，海外待确定
        const domain = 'ald-lamp';
        let url = `//${domain}.alicdn.com/bottom/${appIdArr.sort().join('/')}`;
        const backupParamsObj = parseBackupParams(backupParams);
        Object.keys(backupParamsObj)
            .sort()
            .forEach((key) => {
                url += `/${key}=${backupParamsObj[key]}`;
            });
        url += '/data.jsonp';
        return url;
    } catch (err) {
        console.error('getBottomDataUrl err: ', err);
        return '';
    }
};

const getBottomCallbackName = (params: IAldDataParam): string => {
    try {
        const { resId = '', backupParams = '' } = params || {};
        let resIdArr = resId?.replace(/-/g, '_').split(',');
        resIdArr = resIdArr?.sort();
        let paramStr = '';
        const backupParamsObj = parseBackupParams(backupParams);
        Object.keys(backupParamsObj)
            .sort()
            .forEach((key) => {
                paramStr += `_${key}_${backupParamsObj[key]}`;
            });
        return `callback_${resIdArr.join(',')}${paramStr}`;
    } catch (err) {
        console.error('getBottomCallbackName err: ', err);
        return '';
    }
};

export const requestToLocalCache = async (aldParams: IAldDataParam): Promise<IRes> => {
    const { resId = '' } = aldParams || {};
    const data = await getCacheData(resId);
    return data;
};

export const requestAldBackup = (aldParams: IAldDataParam) => {
    const bottomDataUrl = getBottomDataUrl(aldParams);
    const callbackName = getBottomCallbackName(aldParams);
    const request = (url: string, callbackName: string): Promise<IRes> => {
        return new Promise((resolve) => {
            jsonp(
                url,
                {
                    param: 'callback',
                    prefix: '',
                    name: callbackName,
                    timeout: 3000,
                },
                (err: any, data: any) => {
                    if (data) {
                        const dataParsed = {
                            success: isTrue(data?.success),
                            data: {
                                originResponse: data,
                            },
                        };
                        resolve(dataParsed);
                        return;
                    }
                    if (err) {
                        // console.log(`requestAldBackup ${url} failed: `, err);
                        resolve({
                            success: false,
                            data: err,
                        });
                    }
                },
            );
        });
    };
    return request(bottomDataUrl, callbackName);
};

export const requestStaticBackup = (tag: string): Promise<IRes> => {
    try {
        const data = getNestedProperty((window as any)?.staticConfig || {}, tag);
        return Promise.resolve({
            success: !!data,
            data: {
                originResponse: data,
            },
        });
    } catch (err) {
        // console.log(`requestStaticBackup ${tag} failed: `, err);
        return Promise.resolve({
            success: false,
            data: err,
        });
    }
};

export const commonBackup = async (params: IAldReqConfig) => {
    const { openLocalCache, openAldBottom, staticBottomTags, data } = params || {};
    let error = null;
    if (openLocalCache) {
        const res1 = await requestToLocalCache(data);
        if (res1?.success) {
            return {
                from: IBackupDataFrom.LocalCache,
                res: res1,
            };
        } else {
            error = res1?.data;
        }
    }
    if (openAldBottom) {
        const res2 = await requestAldBackup(data);
        if (res2?.success) {
            return {
                from: IBackupDataFrom.AldBackup,
                res: res2,
            };
        } else {
            error = res2?.data;
        }
    }
    if (staticBottomTags) {
        const res3 = await requestStaticBackup(staticBottomTags);
        if (res3?.success) {
            return {
                from: IBackupDataFrom.Static,
                res: res3,
            };
        } else {
            error = res3?.data;
        }
    }
    return {
        from: IBackupDataFrom.Empty,
        res: error,
    };
};
