import { isTrue } from './';
import { $staticConfig } from './constant';
import { reportCustom } from './jstracker';
import { getLocalStorageItem, setLocalStorageItem } from './storage';

const cacheAbility = $staticConfig?.cacheAbility || {};
const useIndexDB = isTrue(cacheAbility?.useIndexDB);
const useLocalStorage = isTrue(cacheAbility?.useLocalStorage);
const stopSetCache = isTrue(cacheAbility?.stopSetCache);

const DB_NAME = 'PC_INDEX_CLIENT';
let db: IDBDatabase;

export const defaultStoreName = 'dataCache';
export const LOCAL_STORAGE_PREFIX_KEY = 'PC_INDEX_CLIENT';

export interface ICacheData {
    id?: never;
    [key: string]: any;
}

interface IRes {
    success: boolean;
    data: ICacheData | null;
}

export enum IFromType {
    set,
    get,
}

export const generateUniqueLSKey = (key: string, storeName = defaultStoreName) => {
    return `${LOCAL_STORAGE_PREFIX_KEY}_${storeName}_${key}`;
};

const isSupportIndexedDB = () => {
    try {
        // https://github.com/jakearchibald/safari-14-idb-fix/blob/main/src/index.ts
        const isSafari =
            !(navigator as any).userAgentData &&
            /Safari\//.test(navigator.userAgent) &&
            !/Chrom(e|ium)\//.test(navigator.userAgent);
        if (isSafari || !indexedDB) {
            return false;
        }
        return true;
    } catch (e) {
        return false;
    }
};

let initDBPromise: Promise<boolean> | null = null;
const initDB = (storeName = defaultStoreName, dbName = DB_NAME, version = 1): Promise<boolean> => {
    if (initDBPromise) {
        return initDBPromise;
    }
    initDBPromise = new Promise((resolve) => {
        try {
            if (!isSupportIndexedDB()) {
                resolve(false);
                reportCustom({
                    code: 'initDB-failed',
                    message: '[common][cache][indexDB][init]',
                    sampling: 1,
                    c1: JSON.stringify('indexDB not support'),
                });
                return;
            }
            const request = indexedDB.open(dbName, version);
            request.onupgradeneeded = () => {
                // console.log('[initDB] onupgradeneeded');
                const idb = request.result;
                if (!idb.objectStoreNames.contains(storeName)) {
                    // console.log('[initDB] Creating store');
                    idb.createObjectStore(storeName, { keyPath: 'id' });
                }
            };

            request.onsuccess = () => {
                // console.log('[initDB] onsuccess');
                db = request.result;
                resolve(true);
            };

            request.onerror = (event: any) => {
                // console.log('[initDB] onerror');
                resolve(false);
                reportCustom({
                    code: 'initDB-failed',
                    message: '[common][cache][indexDB][init]',
                    sampling: 1,
                    c1: JSON.stringify(event?.target?.error?.message || 'initDB onerror triggered'),
                });
            };
        } catch (err) {
            resolve(false);
            reportCustom({
                code: 'initDB-failed',
                message: '[common][cache][indexDB][init]',
                sampling: 1,
                c1: JSON.stringify(err?.message || 'initDB error triggered'),
            });
        }
    });
    return initDBPromise;
};

export const setCacheData = (key: string, data: ICacheData, storeName = defaultStoreName): Promise<IRes> => {
    return new Promise(async (resolve) => {
        try {
            if (stopSetCache) {
                return resolve({
                    success: false,
                    data: null,
                });
            }
            // 无论什么时候都要set，以保持更新
            const uniqueKey = generateUniqueLSKey(key, storeName);
            const isLocalStorageSuccess = setLocalStorageItem(uniqueKey, data);
            await initDB();
            if (!db) {
                return resolve({
                    success: isLocalStorageSuccess,
                    data: isLocalStorageSuccess ? data : null,
                });
            }
            const tx = db.transaction(storeName, 'readwrite');
            const store = tx.objectStore(storeName);
            const putRequest = store.put({
                ...data,
                id: key,
            });
            putRequest.onsuccess = () => {
                resolve({
                    success: true,
                    data,
                });
            };
            putRequest.onerror = (event: any) => {
                // console.log('[setCacheData] onerror');
                resolve({
                    success: isLocalStorageSuccess,
                    data: isLocalStorageSuccess ? data : null,
                });
                reportCustom({
                    code: 'setCacheData-failed',
                    message: '[common][cache][indexDB][set]',
                    sampling: 1,
                    c1: JSON.stringify(event?.target?.error?.message || 'indexDB onerror triggered'),
                });
            };
        } catch (err) {
            resolve({
                success: false,
                data: null,
            });
            reportCustom({
                code: 'setCacheData-failed',
                message: '[common][cache][set]',
                sampling: 1,
                c1: JSON.stringify(err?.message || 'setCacheData error triggered'),
            });
        }
    });
};

export const getCacheData = (key: string, storeName = defaultStoreName): Promise<IRes> => {
    return new Promise(async (resolve) => {
        try {
            const getCacheFromLS = () => {
                if (useLocalStorage) {
                    const uniqueKey = generateUniqueLSKey(key, storeName);
                    const localStorageKeyData = getLocalStorageItem(uniqueKey);
                    resolve({
                        success: !!localStorageKeyData,
                        data: localStorageKeyData,
                    });
                } else {
                    resolve({
                        success: false,
                        data: null,
                    });
                }
            };

            if (useIndexDB) {
                await initDB();
                if (!db) {
                    return getCacheFromLS();
                }
                const tx = db.transaction(storeName, 'readonly');
                const store = tx.objectStore(storeName);
                const getKeyRequest = store.get(key);
                getKeyRequest.onsuccess = () => {
                    const result = getKeyRequest?.result;
                    resolve({
                        success: !!result,
                        data: result,
                    });
                };
                getKeyRequest.onerror = (event: any) => {
                    // console.log('[getCacheData] onerror');
                    getCacheFromLS();
                    reportCustom({
                        code: 'getCacheData-failed',
                        message: '[common][cache][indexDB][get]',
                        sampling: 1,
                        c1: JSON.stringify(event?.target?.error?.message || 'setCacheData error triggered'),
                    });
                };
            } else {
                return getCacheFromLS();
            }
        } catch (err) {
            resolve({
                success: false,
                data: null,
            });
            reportCustom({
                code: 'getCacheData-failed',
                message: '[common][cache][get]',
                sampling: 1,
                c1: JSON.stringify(err?.message || 'getCacheData error triggered'),
            });
        }
    });
};
