import { defineConfig } from '@ice/app';
import { PegasusPlugin } from '@ali/build-plugin-pegasus-project';

// The project config, see https://ice3.alibaba-inc.com/v3/docs/guide/basic/config
export default defineConfig({
  // Set your configs here.
  plugins: [
    PegasusPlugin({
      previewMode: 'local',
      documentOnly: true,
      runInWormholeContainer: true,
    }),
  ],
  server: {
    // Wormhole only support server bundle with cjs format.
    format: 'cjs',
    bundle: true,
  },
  codeSplitting: 'page',
  ssg: false,
});