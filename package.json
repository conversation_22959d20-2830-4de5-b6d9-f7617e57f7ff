{"name": "@ali/tbpc-tbhome-client", "version": "1.0.34", "description": "ice.js 3 Worm<PERSON> 官方模块", "dependencies": {"@ali/appear": "^1.3.0", "@ali/client-taocoin-enter": "^0.1.5", "@ali/pc-bbs-enter": "^0.1.1", "@ali/pc-tb-guess-u-link-feeds": "2.1.22", "@ali/tbhome-client-order-logistics": "^1.0.2", "@ali/picture": "^2.9.0", "@ali/tbhome-client-shopping-cart": "^1.0.1", "@ali/trade-ttid": "^3.0.0-beta.5", "@ali/wormhole-context": "^1.0.0", "@ice/runtime": "^1.0.0", "dayjs": "^1.11.13", "enquire.js": "^2.1.6", "json2mq": "^0.2.0", "jsonp": "^0.2.1", "react": "^18.2.0", "react-dom": "^18.2.0", "react-slick": "^0.30.3", "slick-carousel": "^1.8.1"}, "devDependencies": {"@ali/build-plugin-pegasus-project": "^2.0.0", "@ali/eslint-config-att": "^1.0.6", "@ali/ice-plugin-def": "^1.0.10", "@ali/ice-plugin-spm": "^3.0.1", "@applint/spec": "^1.2.3", "@ice/app": "^3.0.0", "@ice/plugin-pha": "^3.0.4", "@types/node": "^18.11.17", "@types/react": "^18.0.0", "@types/react-dom": "^18.0.0", "@types/react-slick": "^0.23.13", "eslint": "^8.35.0", "stylelint": "^15.2.0", "typescript": "^4.9.5"}, "scripts": {"start": "ice start", "build": "ice build", "eslint": "eslint ./src --cache --ext .js,.jsx,.ts,.tsx", "eslint:fix": "npm run eslint -- --fix", "stylelint": "stylelint \"src/**/*.{css,scss,less}\" --cache", "stylelint:fix": "npm run stylelint -- --fix"}, "publishConfig": {"registry": "http://registry.npm.alibaba-inc.com"}, "repository": "**************************:tbpc/tbhome-client.git"}